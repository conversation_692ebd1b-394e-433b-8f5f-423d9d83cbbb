import eslintPluginReact from "eslint-plugin-react";
import eslintPluginReactHooks from "eslint-plugin-react-hooks";
import eslintPluginTs from "@typescript-eslint/eslint-plugin";
import parserTs from "@typescript-eslint/parser";
import eslintPluginImport from "eslint-plugin-import";
import prettier from "eslint-config-prettier";

export default [
  {
    ignores: ["dist", "build", "node_modules", ".next", ".turbo", "**/*.d.ts"],
  },
  {
    files: ["**/*.ts", "**/*.tsx"],
    languageOptions: {
      parser: parserTs,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        project: "./tsconfig.json",
      },
    },
    plugins: {
      "@typescript-eslint": eslintPluginTs,
      import: eslintPluginImport,
    },
    rules: {
      ...eslintPluginTs.configs.recommended.rules,

      // Import order rules
      "import/order": [
        "error",
        {
          groups: [
            "external", // npm packages like react, next, etc.
            "builtin", // Node.js built-ins
            "internal", // e.g., @/components or aliases
            ["parent", "sibling", "index"], // relative imports
            "object", // object imports (e.g., import * as _ from "lodash")
            "type", // TypeScript type imports
          ],
          pathGroups: [
            {
              pattern: "react",
              group: "external",
              position: "before",
            },
            {
              pattern: "next/**",
              group: "external",
              position: "before",
            },
            {
              pattern: "@/**",
              group: "internal",
              position: "after",
            },
            {
              pattern: "*.css",
              group: "index",
              patternOptions: { matchBase: true },
              position: "after",
            },
          ],
          pathGroupsExcludedImportTypes: ["builtin"],
          alphabetize: {
            order: "asc",
            caseInsensitive: true,
          },
          "newlines-between": "always",
        },
      ],
    },
  },
  {
    files: ["**/*.js", "**/*.jsx", "**/*.tsx"],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
    },
    plugins: {
      react: eslintPluginReact,
      "react-hooks": eslintPluginReactHooks,
      import: eslintPluginImport,
    },
    rules: {
      ...eslintPluginReact.configs.recommended.rules,
      ...eslintPluginReactHooks.configs.recommended.rules,

      // ⛔ Disable legacy rule not needed for React 17+
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",

      // Import order for JS/JSX/TSX
      "import/order": [
        "error",
        {
          groups: [
            "external", // npm packages like react, next, etc.
            "builtin", // Node.js built-ins
            "internal", // e.g., @/components or aliases
            ["parent", "sibling", "index"], // relative imports
            "object", // object imports (e.g., import * as _ from "lodash")
            "type", // TypeScript type imports
          ],
          pathGroups: [
            {
              pattern: "react",
              group: "external",
              position: "before",
            },
            {
              pattern: "next",
              group: "external",
              position: "before",
            },
            {
              pattern: "next/**",
              group: "external",
              position: "before",
            },
            {
              pattern: "@/**",
              group: "internal",
              position: "after",
            },
            {
              pattern: "*.css",
              group: "index",
              patternOptions: { matchBase: true },
              position: "after",
            },
          ],
          pathGroupsExcludedImportTypes: ["builtin"],
          alphabetize: {
            order: "asc",
            caseInsensitive: true,
          },
          "newlines-between": "always",
        },
      ],
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
  prettier,
];
