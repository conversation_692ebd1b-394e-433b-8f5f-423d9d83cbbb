import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  productionBrowserSourceMaps: true,
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  async redirects() {
    return [
      {
        source: "/size-guide",
        destination: "/size-guide/ring",
        permanent: true,
      },
    ];
  },
  images: {
    deviceSizes: [320, 420, 768, 1024, 1440, 1920],
    loader: "custom",
    path: "",
    loaderFile: "./src/utils/helpers/image-loader.ts",
    remotePatterns: [
      {
        protocol: "http",
        hostname: "localhost",
      },
      {
        protocol: "https",
        hostname: "svaraa-dev-media-bucket.s3.ap-south-1.amazonaws.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "dmz0x99geljvp.cloudfront.net",
        pathname: "/**",
      },
    ],
  },
  webpack(config: {
    module: {
      rules: {
        push: (rule: { test: RegExp; use: string[] }) => void;
      };
    };
  }) {
    // Configure SVGR
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    return config;
  },
};

export default nextConfig;
