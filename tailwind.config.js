const theme = {
  text: {
    primary: "var(--text-primary)",
    secondary: "var(--text-secondary)",
    contrast: "var(--text-contrast)",
  },
  background: {
    primary: "var(--bg-primary)",
    secondary: "var(--bg-secondary)",
    accent: "var(--bg-accent)",
    container: "var(--bg-container)",
    cards: "var(--bg-cards)",
  },
  other: {
    white: "var(--bg-primary)",
    black: "var(--bg-secondary)",
    muted: "var(--muted)",
    border: "var(--border)",
  },
  validations: {
    success: "var(--validations-success)",
    error: "var(--validations-error)",
    warning: "var(--validations-warning)",
  },
};

module.exports = {
  darkMode: ["class", "class"],
  content: [
    "./src/app/**/*.{js,ts,jsx,tsx}",
    "./src/pages/**/*.{js,ts,jsx,tsx}",
    "./src/components/**/*.{js,ts,jsx,tsx}",
    "./src/modules/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@medusajs/ui/dist/**/*.{js,jsx,ts,tsx}",
    "./src/app/globals.css",
  ],
  important: true,
  theme: {
    extend: {
      transitionProperty: {
        width: "width margin",
        height: "height",
        bg: "background-color",
        display: "display opacity",
        visibility: "visibility",
        padding: "padding-top padding-right padding-bottom padding-left",
      },
      letterSpacing: {
        "2.5p": "0.025em",
        "3p": "0.03em",
        "5p": "0.05em",
        "6p": "0.06em",
      },
      colors: {
        theme,
      },
      borderRadius: {
        none: "0px",
        soft: "2px",
        base: "4px",
        rounded: "8px",
        large: "16px",
        circle: "9999px",
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      maxWidth: {
        "8xl": "100rem",
      },
      screens: {
        "2xsmall": "320px",
        xsmall: "512px",
        small: "1024px",
        medium: "1280px",
        large: "1440px",
        xlarge: "1680px",
        "2xlarge": "1920px",
      },
      fontSize: {
        "3xl": "2rem",
      },
      fontFamily: {
        sans: [
          "montserrat",
          "Inter",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Ubuntu",
          "sans-serif",
        ],
        acumin: "var(--font-acumin)",
        montserrat: "var(--font-montserrat)",
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        ".content-container": {
          "@apply mx-auto w-full px-5 lg:px-[50px] max-w-[1440px]": {},
        },
        ".nav-container": {
          "@apply mx-auto w-full px-[15px] lg:px-[46px] max-w-[1440px]": {},
        },
        ".xl-container": {
          "@apply mx-auto w-full px-5 lg:px-[221px] max-w-[1440px]": {},
        },
        ".lg-container": {
          "@apply mx-auto w-full px-5 lg:px-[116px] max-w-[1440px]": {},
        },
        ".md-container": {
          "@apply mx-auto w-full max-w-[876px] px-6 max-w-[1440px]": {},
        },
        ".contrast-btn": {
          "@apply rounded-full border border-black px-4 py-2 transition-colors duration-200 ease-in hover:bg-black hover:text-white":
            {},
        },
        /* custom typography classes */
        ".h1": {
          "font-family": "var(--font-acumin)",
          "font-weight": "700",
          "@apply text-[56px]/[64px] font-bold tracking-3p md:text-[74px]/[74px] md:tracking-2.5p":
            {},
        },
        ".h2": {
          "font-family": "var(--font-acumin)",
          "font-weight": "700",
          "@apply text-[36px]/[44px] tracking-3p md:text-[40px]/[normal]": {},
        },
        ".body-copy": {
          "font-weight": "400",
          "@apply text-[14px]/[24px] tracking-3p md:text-[14px]/[26px]": {},
        },
        ".body-copy-2": {
          "font-weight": "400",
          "@apply text-[12px]/[20px] tracking-3p md:text-[12px]/[22px] md:tracking-5p":
            {},
        },
        ".body-copy-semi-bold": {
          "font-weight": "600",
          "text-transform": "uppercase",
          "text-decoration": "none",
          "@apply text-[14px]/[22px] uppercase tracking-5p md:text-[14px]/[26px] md:tracking-6p":
            {},
        },
        ".cta-copy": {
          "font-weight": "500",
          "text-transform": "uppercase",
          "letter-spacing": "5%",
          "text-decoration": "none",
          "@apply text-[12px] leading-normal": {},
        },
        ".no-scrollbar": {
          "-ms-overflow-style": "none",
          "scrollbar-width": "none",
          "&::-webkit-scrollbar": {
            display: "none",
          },
        },
      };
      addUtilities(newUtilities);
    },
    require("tailwindcss-radix")(),
    require("tailwindcss-animate"),
  ],
};
