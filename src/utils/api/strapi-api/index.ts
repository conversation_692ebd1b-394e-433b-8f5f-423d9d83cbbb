/* eslint-disable @typescript-eslint/no-explicit-any */
import { executeQuery } from "utils/helpers/query";
import { getPageTemplateNameQuery } from "utils/strapi-api/page-template-query";
import { PageTypeEnum, SeoType } from "utils/types/common";

// done
export const getTemplateBlocks = async (
  slug: string,
  query: string
): Promise<any[]> => {
  try {
    const result: any = await executeQuery(query, {
      slug,
      currentTime: new Date().toISOString(),
    });

    const templateData = result?.pageTemplates?.[0];
    if (!templateData) {
      console.warn(`-> No page template found for slug: ${slug}`);
      return [];
    }

    return templateData?.blocks;
  } catch (error) {
    console.error(`-> Error fetching template blocks for slug ${slug}:`, error);
    return [];
  }
};

export const getPageTemplateName = async (
  page: string
): Promise<{
  template_name?: string;
  page_type?: PageTypeEnum;
  seo?: SeoType;
}> => {
  try {
    const result: any = await executeQuery(getPageTemplateNameQuery, {
      slug: page,
    });

    const data = result?.pages;
    if (data.length === 0) return {};

    const page_data = data[0];
    const template_name = page_data?.page_template?.template_name;
    const page_type = page_data?.page_enum;
    const seo = page_data?.seo;

    const res = {
      template_name,
      page_type,
      seo,
    };

    return res;
  } catch (error) {
    console.error("Error fetching template name:", error);
    return {};
  }
};
