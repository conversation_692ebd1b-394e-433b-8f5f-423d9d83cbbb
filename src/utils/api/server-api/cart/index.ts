/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import { revalidateTag } from "next/cache";
import { redirect } from "next/navigation";

import { HttpTypes } from "@medusajs/types";
import { AddressesFormSchema } from "@modules/checkout/components/address-step/utils/types";
import {
  getAuthHeaders,
  getCacheOptions,
  getCacheTag,
  getCartId,
  removeCartId,
  setCartId,
} from "utils/helpers/cookies";
import { sdk } from "utils/helpers/medusa-config";
import medusaError from "utils/helpers/medusa-error";

import { listCartShippingMethods } from "../fulfillment";
import { listCartPaymentMethods } from "../payment";
import { getRegion } from "../regions";

/**
 * Retrieves a cart by its ID. If no ID is provided, it will use the cart ID from the cookies.
 * @param cartId - optional - The ID of the cart to retrieve.
 * @returns The cart object if found, or null if not found.
 */
export async function retrieveCart(cartId?: string) {
  const id = cartId || (await getCartId());

  if (!id) {
    return null;
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  const next = {
    ...(await getCacheOptions("carts")),
  };

  return await sdk.client
    .fetch<HttpTypes.StoreCartResponse>(`/store/carts/${id}`, {
      method: "GET",
      query: {
        fields:
          "*items, *region, *items.product, *items.variant, *items.thumbnail, *items.metadata, +items.total, *promotions, +shipping_methods.name",
      },
      headers,
      next,
      cache: "force-cache",
    })
    .then(({ cart }) => cart)
    .catch(() => null);
}

export async function getOrSetCart() {
  const countryCode = process.env.NEXT_PUBLIC_DEFAULT_REGION || "in";
  const region = await getRegion();

  if (!region) {
    throw new Error(`Region not found for country code: ${countryCode}`);
  }

  let cart = await retrieveCart();

  const headers = {
    ...(await getAuthHeaders()),
  };

  if (!cart) {
    const cartResp = await sdk.store.cart.create(
      { region_id: region.id },
      {},
      headers
    );
    cart = cartResp.cart;

    await setCartId(cart.id);

    const cartCacheTag = await getCacheTag("carts");
    revalidateTag(cartCacheTag);
  }

  if (cart && cart?.region_id !== region.id) {
    await sdk.store.cart.update(cart.id, { region_id: region.id }, {}, headers);
    const cartCacheTag = await getCacheTag("carts");
    revalidateTag(cartCacheTag);
  }

  return cart;
}

export async function updateCart(data: HttpTypes.StoreUpdateCart) {
  const cartId = await getCartId();

  if (!cartId) {
    throw new Error(
      "No existing cart found, please create one before updating"
    );
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.cart
    .update(cartId, data, {}, headers)
    .then(async ({ cart }) => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
      return cart;
    })
    .catch((e) => {
      throw medusaError(e);
    });
}

export async function addToCart({
  variantId,
  quantity,
}: {
  variantId: string;
  quantity: number;
}) {
  if (!variantId) {
    throw new Error("Missing variant ID when adding to cart");
  }

  const cart = await getOrSetCart();

  if (!cart) {
    throw new Error("Error retrieving or creating cart");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  await sdk.store.cart
    .createLineItem(
      cart.id,
      {
        variant_id: variantId,
        quantity,
      },
      {},
      headers
    )
    .then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    })
    .catch(medusaError);
}

export async function updateLineItem({
  lineId,
  quantity,
}: {
  lineId: string;
  quantity: number;
}) {
  if (!lineId) {
    throw new Error("Missing lineItem ID when updating line item");
  }

  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("Missing cart ID when updating line item");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  await sdk.store.cart
    .updateLineItem(cartId, lineId, { quantity }, {}, headers)
    .then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    })
    .catch(medusaError);
}

export async function deleteLineItem(lineId: string) {
  if (!lineId) {
    throw new Error("Missing lineItem ID when deleting line item");
  }

  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("Missing cart ID when deleting line item");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  await sdk.store.cart
    .deleteLineItem(cartId, lineId, headers)
    .then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    })
    .catch(medusaError);
}

export async function setShippingMethod({
  cartId,
  shippingMethodId,
}: {
  cartId: string;
  shippingMethodId: string;
}) {
  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.cart
    .addShippingMethod(cartId, { option_id: shippingMethodId }, {}, headers)
    .then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    })
    .catch((e) => {
      throw medusaError(e);
    });
}

export async function initiatePaymentSession(
  cart: HttpTypes.StoreCart,
  data: {
    provider_id: string;
    context?: Record<string, unknown>;
  }
) {
  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.payment
    .initiatePaymentSession(cart, data, {}, headers)
    .then(async (resp) => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
      return resp;
    })
    .catch(medusaError);
}

export async function applyPromotions(codes: string[]) {
  const cartId = await getCartId();

  if (!cartId) {
    throw new Error("No existing cart found");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  return sdk.store.cart
    .update(cartId, { promo_codes: codes }, {}, headers)
    .then(async () => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
    })
    .catch(medusaError);
}

export async function submitPromotionForm(
  currentState: unknown,
  formData: FormData
) {
  const code = formData.get("code") as string;
  try {
    await applyPromotions([code]);
  } catch (e: any) {
    return e.message;
  }
}

export async function handleOrder(formData: AddressesFormSchema) {
  try {
    if (!formData) {
      throw new Error("No form data found when setting addresses");
    }
    const cartId = await getCartId();
    if (!cartId) {
      throw new Error("No existing cart found when setting addresses");
    }

    const data = {
      shipping_address: {
        first_name: formData.shipping_address.firstName,
        last_name: formData.shipping_address.lastName,
        address_1: formData.shipping_address.addressLine1,
        address_2: formData.shipping_address.addressLine2,
        company: formData.shipping_address.title,
        postal_code: formData.shipping_address.postcode,
        city: formData.shipping_address.city,
        country_code: formData.shipping_address.country,
        province: formData.shipping_address.state,
        phone: formData.shipping_address.phone,
      },
    } as any;

    const sameAsBilling = formData.sameAsShipping;
    if (sameAsBilling) data.billing_address = data.shipping_address;

    if (!sameAsBilling)
      data.billing_address = {
        first_name: formData?.billing_address?.firstName,
        last_name: formData?.billing_address?.lastName,
        address_1: formData?.billing_address?.addressLine1,
        address_2: formData?.billing_address?.addressLine2,
        company: formData?.billing_address?.title,
        postal_code: formData?.billing_address?.postcode,
        city: formData?.billing_address?.city,
        country_code: formData?.billing_address?.country,
        province: formData?.billing_address?.state,
        phone: formData?.billing_address?.phone,
      };

    const shippingMethods = await listCartShippingMethods(cartId);

    if (!shippingMethods || shippingMethods.length === 0) {
      throw new Error("No shipping methods found when placing an order");
    }

    await setShippingMethod({
      cartId: cartId,
      shippingMethodId: shippingMethods[0].id,
    });

    const cart = await updateCart(data);

    const paymentMethods = await listCartPaymentMethods(cart.region?.id ?? "");

    if (!paymentMethods || paymentMethods.length === 0) {
      throw new Error("No payment methods found when placing an order");
    }

    const activeSession = cart.payment_collection?.payment_sessions?.find(
      (paymentSession: HttpTypes.StorePaymentSession) =>
        paymentSession.status === "pending"
    );

    if (!activeSession) {
      await initiatePaymentSession(cart, {
        provider_id: paymentMethods[0].id,
      });
    }

    if (!paymentMethods || paymentMethods.length === 0) {
      throw new Error("No payment methods found when placing an order");
    }

    const notReady =
      !cart ||
      !cart.shipping_address ||
      !cart.billing_address ||
      !cart.email ||
      (cart.shipping_methods?.length ?? 0) < 1;

    if (notReady) {
      throw new Error("Cart is not ready to be placed");
    }

    try {
      await placeOrder();
    } catch (e: any) {
      throw new Error("Error placing order: " + e.message);
    }
  } catch (e: any) {
    return e.message;
  }
}

/**
 * Places an order for a cart. If no cart ID is provided, it will use the cart ID from the cookies.
 * @param cartId - optional - The ID of the cart to place an order for.
 * @returns The cart object if the order was successful, or null if not.
 */
export async function placeOrder(cartId?: string) {
  const id = cartId || (await getCartId());

  if (!id) {
    throw new Error("No existing cart found when placing an order");
  }

  const headers = {
    ...(await getAuthHeaders()),
  };

  const cartRes = await sdk.store.cart
    .complete(id, {}, headers)
    .then(async (cartRes) => {
      const cartCacheTag = await getCacheTag("carts");
      revalidateTag(cartCacheTag);
      return cartRes;
    })
    .catch((e) => {
      throw medusaError(e);
    });

  if (cartRes?.type === "order") {
    const countryCode =
      cartRes.order.shipping_address?.country_code?.toLowerCase();
    removeCartId();
    redirect(`/${countryCode}/order/${cartRes?.order.id}/confirmed`);
  }

  return cartRes.cart;
}

/**
 * Updates the countrycode param and revalidates the regions cache
 * @param regionId
 * @param countryCode
 */
export async function updateRegion(countryCode: string, currentPath: string) {
  const cartId = await getCartId();
  const region = await getRegion();

  if (!region) {
    throw new Error(`Region not found for country code: ${countryCode}`);
  }

  if (cartId) {
    await updateCart({ region_id: region.id });
    const cartCacheTag = await getCacheTag("carts");
    revalidateTag(cartCacheTag);
  }

  const regionCacheTag = await getCacheTag("regions");
  revalidateTag(regionCacheTag);

  const productsCacheTag = await getCacheTag("products");
  revalidateTag(productsCacheTag);

  redirect(`/${countryCode}${currentPath}`);
}

export async function listCartOptions() {
  const cartId = await getCartId();
  const headers = {
    ...(await getAuthHeaders()),
  };
  const next = {
    ...(await getCacheOptions("shippingOptions")),
  };

  return await sdk.client.fetch<{
    shipping_options: HttpTypes.StoreCartShippingOption[];
  }>("/store/shipping-options", {
    query: { cart_id: cartId },
    next,
    headers,
    cache: "force-cache",
  });
}
