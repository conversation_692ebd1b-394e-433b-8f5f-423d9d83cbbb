export type MediaType = "image" | "video" | "gif";

export interface MediaFormat {
  name: string;
  hash: string;
  ext: string;
  mime: string;
  width: number;
  height: number;
  size: number;
  path?: string;
  url: string;
  sizeInBytes?: number;
}

export interface MediaFormats {
  thumbnail?: MediaFormat;
  small?: MediaFormat;
  medium?: MediaFormat;
  large?: MediaFormat;
}

export interface Media {
  documentId: string;
  name: string;
  alternativeText: string;
  caption: string | null;
  width: number;
  height: number;
  formats: MediaFormats | unknown | null;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata?: unknown | null;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

export interface LegacyMedia {
  id: number;
  name: string;
  alternativeText: string;
  caption: string;
  width: number;
  height: number;
  formats: {
    thumbnail: MediaFormat;
    small: MediaFormat;
    medium: MediaFormat;
    large: MediaFormat;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string;
  provider: string;
  createdAt: Date;
  updatedAt: Date;
}

export type IconProps = {
  color?: string;
  size?: string | number;
} & React.SVGAttributes<SVGElement>;

export enum PageTypeEnum {
  Home = "HOME",
  Store = "STORE",
}

export interface SeoType {
  id: string;
  metaTitle: string;
  metaDescription: string;
  metaImage: Media;
  keywords: string;
  canonicalURL: string;
}

export interface NestedSubMenu {
  id: string;
  title: string;
  slug: string;
}

export interface SubMenu {
  id: string;
  title: string;
  slug: string;
  type?: string;
  nested_sub_menu?: NestedSubMenu[];
}

export interface BannerType {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  cta_title?: string;
  cta_link?: string;
  desktop_media: Media;
  mobile_media: Media;
  thumbnail: Media;
}

export type DisplayType = "desktop" | "phone" | "mobile" | "both";

export type ResponsiveType = "desktop" | "mobile" | "both";
