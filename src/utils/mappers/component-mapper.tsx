import dynamic from "next/dynamic";

import CollectionDetails from "@modules/collections/components/collection-details";
import CollectionShowcase from "@modules/collections/components/collection-showcase";
import HeroMediaCenterBanner from "@modules/collections/components/hero-media-center-banner";
import PressAndHold from "@modules/collections/components/press-and-hold";
import CenterBanner from "@modules/common/center-banner";
import FAQs from "@modules/faqs/components/faq-item";
import GiftPromotion from "@modules/gifting/components/gift-promotion";
import GiftingCategories from "@modules/gifting/components/gifting-categories";
import ShopByPriceRange from "@modules/gifting/components/shop-by-range";
import SpecialGifts from "@modules/gifting/components/special-gift";
import FFBookAppointmentSection from "@modules/home/<USER>/appointment-section";
import FFBrandTagline from "@modules/home/<USER>/brand-tagline";
import FFCategoryShowcase from "@modules/home/<USER>/category-showcase";
import FFFeaturedInSection from "@modules/home/<USER>/featured-in";
import FFGiftPromotion from "@modules/home/<USER>/gift-promotion";
import FFHeroBanner from "@modules/home/<USER>/hero-banner";
import FFKeyBenefitsBar from "@modules/home/<USER>/key-benefits-bar";
import FFPromotionalBanner from "@modules/home/<USER>/promotional-banner";
import FFPromotionalTagline from "@modules/home/<USER>/promotional-tagline";
import FFSustainableSection from "@modules/home/<USER>/sustainable-section";
import FFTrendingSection from "@modules/home/<USER>/trending-section";
import LegalPageContent from "@modules/legals/components/legal-content";
import PressArticleCards from "@modules/press/components/press-cards";
import PressHero from "@modules/press/components/press-hero";
import ProductDetailsHero from "@modules/product-details/components/hero";
import SizeGuideHero from "@modules/size-guide/components/hero";
import ProductListingBanner from "@modules/store/components/product-listing-banner";
import RelatedProductSections from "@modules/store/components/related-product-section";

const componentMapper = {
  ComponentHomeHeroBanner: {
    FF: FFHeroBanner,
    Lazy: dynamic(() => import("@modules/home/<USER>/hero-banner")),
  },
  ComponentHomeBrandTagline: {
    FF: FFBrandTagline,
    Lazy: dynamic(() => import("@modules/home/<USER>/brand-tagline")),
  },
  ComponentHomePromotionalBanner: {
    FF: FFPromotionalBanner,
    Lazy: dynamic(() => import("@modules/home/<USER>/promotional-banner")),
  },
  ComponentHomeSustainabilitySection: {
    FF: FFSustainableSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/sustainable-section")),
  },
  ComponentHomeCategoryShowcase: {
    FF: FFCategoryShowcase,
    Lazy: dynamic(() => import("@modules/home/<USER>/category-showcase")),
  },
  ComponentHomeTrendingSection: {
    FF: FFTrendingSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/trending-section")),
  },
  ComponentHomeGiftPromotions: {
    FF: FFGiftPromotion,
    Lazy: dynamic(() => import("@modules/home/<USER>/gift-promotion")),
  },
  ComponentHomeAppointmentSection: {
    FF: FFBookAppointmentSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/appointment-section")),
  },
  ComponentHomeMayaveFeaturedIn: {
    FF: FFFeaturedInSection,
    Lazy: dynamic(() => import("@modules/home/<USER>/featured-in")),
  },
  ComponentHomePromotionalTagline: {
    FF: FFPromotionalTagline,
    Lazy: dynamic(() => import("@modules/home/<USER>/promotional-tagline")),
  },
  ComponentHomeBenefitsBar: {
    FF: FFKeyBenefitsBar,
    Lazy: dynamic(() => import("@modules/home/<USER>/key-benefits-bar")),
  },
  ComponentCollectionPlpHeroCenterBanner: {
    FF: HeroMediaCenterBanner,
    Lazy: dynamic(
      () => import("@modules/collections/components/hero-media-center-banner")
    ),
  },
  ComponentCollectionPlpCollectionShowcase: {
    FF: CollectionShowcase,
    Lazy: dynamic(
      () => import("@modules/collections/components/collection-showcase")
    ),
  },
  ComponentCollectionPlpPressHold: {
    FF: PressAndHold,
    Lazy: dynamic(
      () => import("@modules/collections/components/press-and-hold")
    ),
  },
  ComponentCollectionPlpCollectionDetails: {
    FF: CollectionDetails,
    Lazy: dynamic(
      () => import("@modules/collections/components/collection-details")
    ),
  },
  ComponentCommonCenterBanner: {
    FF: CenterBanner,
    Lazy: dynamic(() => import("@modules/common/center-banner")),
  },
  ComponentFaQsFaq: {
    FF: FAQs,
    Lazy: dynamic(() => import("@modules/faqs/components/faq-item")),
  },
  ComponentStoreProductListingBanner: {
    FF: ProductListingBanner,
    Lazy: dynamic(
      () => import("@modules/store/components/product-listing-banner")
    ),
  },
  ComponentGiftingGiftingCategory: {
    FF: GiftingCategories,
    Lazy: dynamic(
      () => import("@modules/gifting/components/gifting-categories")
    ),
  },
  ComponentGiftingSpecialGifts: {
    FF: SpecialGifts,
    Lazy: dynamic(() => import("@modules/gifting/components/special-gift")),
  },
  ComponentGiftingShopByPrice: {
    FF: ShopByPriceRange,
    Lazy: dynamic(() => import("@modules/gifting/components/shop-by-range")),
  },
  ComponentGiftingGiftPromotionCard: {
    FF: GiftPromotion,
    Lazy: dynamic(() => import("@modules/gifting/components/gift-promotion")),
  },

  ComponentLegalLegalPageContent: {
    FF: LegalPageContent,
    Lazy: dynamic(() => import("@modules/legals/components/legal-content")),
  },
  ComponentPressPressHero: {
    FF: PressHero,
    Lazy: dynamic(() => import("@modules/press/components/press-hero")),
  },
  ComponentPressPressArticleCards: {
    FF: PressArticleCards,
    Lazy: dynamic(() => import("@modules/press/components/press-cards")),
  },
  ComponentPdpProductDetails: {
    FF: ProductDetailsHero,
    Lazy: dynamic(() => import("@modules/product-details/components/hero")),
  },
  ComponentStoreRelatedProductSection: {
    FF: RelatedProductSections,
    Lazy: dynamic(
      () => import("@modules/store/components/related-product-section")
    ),
  },
  ComponentSizeGuideSizeGuideHero: {
    FF: SizeGuideHero,
    Lazy: dynamic(() => import("@modules/size-guide/components/hero")),
  },
};

export default componentMapper;

export type ComponentMapper = keyof typeof componentMapper;
