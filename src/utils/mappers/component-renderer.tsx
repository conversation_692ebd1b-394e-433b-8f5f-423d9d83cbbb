import { DynamicPageBlock } from "@modules/dynamic-page/utils/types";

import componentMapper, { ComponentMapper } from "./component-mapper";

const renderBlock = (
  block: DynamicPageBlock,
  index: number,
  page: string,
  page_type: string,
  template?: string
) => {
  const ComponentInfo = componentMapper[block.__typename as ComponentMapper];
  if (!ComponentInfo) return null;

  const Component = index < 3 ? ComponentInfo.FF : ComponentInfo.Lazy;

  const common_props = {
    block,
    page,
    page_type,
    isFirst: index === 0,
    template,
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return <Component key={`block-${index}`} {...(common_props as any)} />;
};

export default renderBlock;
