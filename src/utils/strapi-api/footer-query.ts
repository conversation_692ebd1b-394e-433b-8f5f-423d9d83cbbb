export const getFooterBlockQuery = /* GraphQL */ `
  query Footer {
    footer {
      documentId
      accent_logo {
        alternativeText
        url
      }
      social_links {
        id
        title
        icon {
          alternativeText
          url
        }
        handle
        display_type
      }
      contrast_logo {
        alternativeText
        url
      }
      footer_nav_link {
        id
        title
        slug
        sub_menu {
          id
          title
          slug
          type
          nested_sub_menu {
            id
            title
            slug
          }
        }
        menu_media {
          id
          title
          subtitle
          cta_title
          cta_link
          desktop_media {
            alternativeText
            url
          }
          mobile_media {
            alternativeText
            url
          }
          description
          responsive
        }
      }
      legel_links {
        id
        title
        logo {
          alternativeText
          url
        }
        slug
        display_type
      }
      legel_link_logo {
        alternativeText
        url
      }
      createdAt
      updatedAt
      publishedAt
    }
  }
`;
