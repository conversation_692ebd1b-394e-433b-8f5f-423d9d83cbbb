export const getCollectionPLPTemplateBlocksQuery = /* GraphQL */ `
  query GetCollectionPLPTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentCollectionPlpHeroCenterBanner {
          id
          __typename
          media {
            url
            alternativeText
          }
          cta_title
          cta_link
        }
        ... on ComponentCollectionPlpCollectionShowcase {
          id
          __typename
          collection_showcase_section {
            __typename
            id
            title
            tagline
            description
            images {
              id
              description
              media {
                alternativeText
                url
              }
            }
          }
        }

        ... on ComponentCollectionPlpPressHold {
          id
          __typename
          press_and_hold_section {
            __typename
            id
            background_video {
              alternativeText
              url
            }
            title
            default_icon {
              alternativeText
              url
            }
            pressed_icon {
              alternativeText
              url
            }
          }
        }

        ... on ComponentCollectionPlpCollectionDetails {
          id
          __typename
          collection_details_section {
            __typename
            id
            description
            column_image {
              alternativeText
              url
            }
            cell_media {
              alternativeText
              url
            }
          }
        }

        ... on ComponentStorePromotionalBanner {
          id
          __typename
          promotional_banner {
            __typename
            id
            module
            section
            media {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                alternativeText
                url
              }
              mobile_media {
                alternativeText
                url
              }
              description
              responsive
            }
            index
          }
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
