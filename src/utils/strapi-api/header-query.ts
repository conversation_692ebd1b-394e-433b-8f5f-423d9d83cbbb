export const getHeaderBlocksQuery = /* GraphQL */ `
  query Header {
    header {
      documentId
      nav_action_links {
        id
        title
        accent_logo {
          alternativeText
          url
        }
        contrast_logo {
          alternativeText
          url
        }
        logo {
          alternativeText
          url
        }
        slug
        display_type
      }
      accent_logo {
        alternativeText
        url
      }
      contrast_logo {
        alternativeText
        url
      }
      title
      main_navigation {
        id
        title
        slug
        sub_menu {
          id
          title
          slug
          nested_sub_menu {
            id
            title
            slug
          }
        }
        menu_media {
          id
          title
          subtitle
          cta_title
          cta_link
          desktop_media {
            alternativeText
            url
          }
          mobile_media {
            alternativeText
            url
          }
          description
          responsive
        }
      }
      createdAt
      updatedAt
      publishedAt
    }
  }
`;
