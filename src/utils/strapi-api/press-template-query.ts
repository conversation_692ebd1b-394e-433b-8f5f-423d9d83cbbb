export const getPressTemplateBlocksQuery = /* GraphQL */ `
  query GetPressTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentPressPressHero {
          id
          heading
          subheading
          __typename
        }

        ... on ComponentHomePromotionalTagline {
          id
          __typename
          promotional_tagline {
            __typename
            title
            subtitle
            description
            cta_title
            cta_link
            richtext_description
          }
        }

        ... on ComponentHomeBenefitsBar {
          __typename
          id
          benefits_bar {
            id
            title
            __typename
            logo {
              url
              height
              width
              alternativeText
              mime
              ext
            }
          }
        }

        ... on ComponentPressPressArticleCards {
          id
          __typename
          article_cards {
            id
            tag
            publish_date
            thumbnail {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                alternativeText
                url
              }
              mobile_media {
                alternativeText
                url
              }
              description
              responsive
            }
          }
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
