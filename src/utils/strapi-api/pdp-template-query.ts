export const getPDPTemplateBlocksQuery = /* GraphQL */ `
  query GetPDPTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentPdpProductDetails {
          id
          __typename
          product_title
          product_description
          product_price
          hero {
            id
            title
            subtitle
            cta_title
            cta_link
            desktop_media {
              alternativeText
              url
            }
            mobile_media {
              alternativeText
              url
            }
            description
            responsive
          }
          product_image {
            id
            title
            subtitle
            cta_title
            cta_link
            desktop_media {
              alternativeText
              url
            }
            mobile_media {
              alternativeText
              url
            }
            description
            responsive
          }
        }

        ... on ComponentStoreRelatedProductSection {
          id
          __typename
          heading
          related_products {
            id
            product_image {
              alternativeText
              url
            }
            Heading
            description
            price
          }
        }

        ... on ComponentHomePromotionalTagline {
          id
          __typename
          promotional_tagline {
            __typename
            title
            subtitle
            description
            cta_title
            cta_link
            richtext_description
          }
        }

        ... on ComponentHomeBenefitsBar {
          __typename
          id
          benefits_bar {
            id
            title
            __typename
            logo {
              url
              height
              width
              alternativeText
              mime
              ext
            }
          }
        }

        ... on ComponentHomeGiftPromotions {
          id
          __typename
          gift_promotion {
            id
            module
            section
            __typename
            media {
              id
              title
              description
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomeSustainabilitySection {
          id
          __typename
          sustainable_section {
            id
            module
            section
            __typename
            media {
              id
              title
              description
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
