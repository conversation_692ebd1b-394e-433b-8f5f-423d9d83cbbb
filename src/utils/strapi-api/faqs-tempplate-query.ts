export const getFAQsTemplateBlocksQuery = /* GraphQL */ `
  query GetFAQsTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentCommonCenterBanner {
          id
          heading
          description
          subtitle
          richtext_description
          theme
          __typename
        }
        ... on ComponentFaQsFaq {
          __typename
          id
          faq_content {
            id
            Topic
            QnA {
              Answer
              Question
              id
            }
          }
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
