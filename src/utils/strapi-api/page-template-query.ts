export const getTemplateBlocksQuery = /* GraphQL */ `
  query GetTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentHomeHeroBanner {
          id
          __typename
          hero_banner {
            module
            section
            media {
              cta_title
              cta_link
              id
              title
              subtitle
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
              thumbnail {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomeBrandTagline {
          id
          __typename
          brand_tagline {
            __typename
            title
            subtitle
            description
          }
        }

        ... on ComponentHomePromotionalBanner {
          id
          __typename
          promotional_banner {
            id
            module
            section
            __typename
            media {
              id
              title
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomeSustainabilitySection {
          id
          __typename
          sustainable_section {
            id
            module
            section
            __typename
            media {
              id
              title
              description
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomeCategoryShowcase {
          id
          __typename
          category_showcase {
            id
            module
            section
            __typename
            media {
              id
              title
              description
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomeGiftPromotions {
          id
          __typename
          gift_promotion {
            id
            module
            section
            __typename
            media {
              id
              title
              description
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomeAppointmentSection {
          id
          __typename
          appointment_section {
            id
            module
            section
            __typename
            media {
              id
              title
              description
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomeMayaveFeaturedIn {
          id
          __typename
          mayave_featured_in {
            id
            title
            subtitle
            __typename
            description
            logo {
              url
              height
              width
              alternativeText
              mime
              ext
            }
          }
        }

        ... on ComponentHomePromotionalTagline {
          id
          __typename
          promotional_tagline {
            __typename
            title
            subtitle
            description
            cta_title
            cta_link
            richtext_description
          }
        }

        ... on ComponentHomeBenefitsBar {
          __typename
          id
          benefits_bar {
            id
            title
            __typename
            logo {
              url
              height
              width
              alternativeText
              mime
              ext
            }
          }
        }

        ... on ComponentHomeTrendingSection {
          id
          __typename
          trending_media {
            id
            __typename
            id
            title
            description
            cta_title
            cta_link
            desktop_media {
              url
              mime
              ext
              height
              width
            }
            mobile_media {
              url
              mime
              ext
              height
              width
            }
          }
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;

export const getPageTemplateNameQuery = /* GraphQL */ `
  query GetPageTemplateName($slug: String) {
    pages(filters: { slug: { eq: $slug } }) {
      documentId
      createdAt
      publishedAt
      page_enum
      slug
      page_template {
        template_name
      }
      seo {
        id
        metaTitle
        metaDescription
        metaImage {
          alternativeText
          url
        }
        keywords
        canonicalURL
      }
    }
  }
`;
