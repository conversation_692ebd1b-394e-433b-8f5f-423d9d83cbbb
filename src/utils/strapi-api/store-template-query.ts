export const getStoreTemplateBlocksQuery = /* GraphQL */ `
  query GetStoreTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentStoreRelatedProductSection {
          id
          __typename
          heading
          related_products {
            id
            product_image {
              documentId
              name
              alternativeText
              caption
              width
              height
              formats
              hash
              ext
              mime
              size
              url
              previewUrl
              provider
              provider_metadata
              createdAt
              updatedAt
              publishedAt
            }
            Heading
            description
            price
          }
        }

        ... on ComponentStorePromotionalBanner {
          id
          __typename
          promotional_banner {
            id
            __typename
            module
            section
            media {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                documentId
                name
                alternativeText
                caption
                width
                height
                formats
                hash
                ext
                mime
                size
                url
                previewUrl
                provider
                provider_metadata
                createdAt
                updatedAt
                publishedAt
              }
              mobile_media {
                documentId
                name
                alternativeText
                caption
                width
                height
                formats
                hash
                ext
                mime
                size
                url
                previewUrl
                provider
                provider_metadata
                createdAt
                updatedAt
                publishedAt
              }
              description
              responsive
            }
            index
          }
        }
        ... on ComponentStoreProductListingBanner {
          id
          __typename
          store_banner {
            id
            module
            section
            media {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                documentId
                name
                alternativeText
                caption
                width
                height
                formats
                hash
                ext
                mime
                size
                url
                previewUrl
                provider
                provider_metadata
                createdAt
                updatedAt
                publishedAt
              }
              mobile_media {
                documentId
                name
                alternativeText
                caption
                width
                height
                formats
                hash
                ext
                mime
                size
                url
                previewUrl
                provider
                provider_metadata
                createdAt
                updatedAt
                publishedAt
              }
              description
              responsive
            }
            index
          }
        }

        ... on ComponentHomeSustainabilitySection {
          id
          __typename
          sustainable_section {
            id
            module
            section
            __typename
            media {
              id
              title
              description
              cta_title
              cta_link
              desktop_media {
                url
                mime
                ext
                height
                width
              }
              mobile_media {
                url
                mime
                ext
                height
                width
              }
            }
          }
        }

        ... on ComponentHomePromotionalTagline {
          id
          __typename
          promotional_tagline {
            __typename
            title
            subtitle
            description
            cta_title
            cta_link
            richtext_description
          }
        }

        ... on ComponentHomeBenefitsBar {
          __typename
          id
          benefits_bar {
            id
            title
            __typename
            logo {
              url
              height
              width
              alternativeText
              mime
              ext
            }
          }
        }

        ... on ComponentHomeTrendingSection {
          id
          __typename
          trending_media {
            id
            __typename
            id
            title
            description
            cta_title
            cta_link
            desktop_media {
              url
              mime
              ext
              height
              width
            }
            mobile_media {
              url
              mime
              ext
              height
              width
            }
          }
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
