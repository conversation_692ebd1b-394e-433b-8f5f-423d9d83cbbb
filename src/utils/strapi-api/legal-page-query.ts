export const getLegalPageTemplateBlocksQuery = /* GraphQL */ `
  query GetLegalPageTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentCommonCenterBanner {
          id
          heading
          description
          subtitle
          richtext_description
          theme
          __typename
        }
        ... on ComponentLegalLegalPageContent {
          id
          legal_content
          __typename
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
