export const getGiftingPageTemplateBlocksQuery = /* GraphQL */ `
  query GetGiftingPageTemplateBlocks($slug: String!) {
    pageTemplates(filters: { template_name: { eq: $slug } }) {
      documentId
      blocks {
        ... on ComponentStoreProductListingBanner {
          id
          __typename
          store_banner {
            id
            module
            section
            media {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                alternativeText
                url
              }
              mobile_media {
                alternativeText
                url
              }
              description
              responsive
              theme
            }
            index
          }
        }
        ... on ComponentGiftingGiftingCategory {
          id
          __typename
          heading
          subheading
          gift_category_cards {
            id
            gift_card {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                alternativeText
                url
              }
              mobile_media {
                alternativeText
                url
              }
              description
              responsive
              theme
            }
          }
        }

        ... on ComponentGiftingSpecialGifts {
          id
          __typename
          heading
          special_gift_card {
            id
            gift_card {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                alternativeText
                url
              }
              description
              responsive
              theme
            }
          }
        }

        ... on ComponentGiftingShopByPrice {
          id
          __typename
          title
          price_range_cta {
            id
            title
            href
          }
        }

        ... on ComponentGiftingGiftPromotionCard {
          id
          __typename
          gift_card_promotion {
            id
            module
            section
            media {
              id
              title
              subtitle
              cta_title
              cta_link
              desktop_media {
                alternativeText
                url
              }

              description
              responsive

              theme
            }
            index
          }
        }
      }
      template_name
      createdAt
      publishedAt
    }
  }
`;
