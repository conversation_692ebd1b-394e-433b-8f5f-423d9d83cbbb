import { useEffect, useState } from "react";

export const useResponsiveDetection = ({
  mobileBreakpoint = 768,
}: {
  mobileBreakpoint: number;
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () =>
      setIsMobile(window.innerWidth < mobileBreakpoint);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return isMobile;
};
