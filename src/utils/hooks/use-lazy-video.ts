import { useEffect } from "react";

const useLazyVideo = ({
  containerRef,
  autoPlay = true,
  muted = true,
}: {
  containerRef: React.RefObject<HTMLDivElement>;
  autoPlay?: boolean;
  muted?: boolean;
}) => {
  useEffect(() => {
    const lazyVideos = containerRef.current?.querySelectorAll(".lazy-video");
    if (!lazyVideos || lazyVideos.length === 0) return;

    if ("IntersectionObserver" in window) {
      const videoObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const video = entry.target as HTMLVideoElement;
            const source = video.querySelector("source");

            if (source && source.dataset.src) {
              source.src = source.dataset.src;
              video.muted = muted;
              video.load();
              if (autoPlay) {
                video.play().catch((error) => {
                  console.warn("Video autoplay failed:", error);
                });
              }
            }

            observer.unobserve(video);
          }
        });
      });

      lazyVideos.forEach((video) => videoObserver.observe(video));
    } else {
      lazyVideos.forEach((video) => {
        const source = video.querySelector("source");
        if (source && source.dataset.src) {
          source.src = source.dataset.src;
          const videoElement = video as HTMLVideoElement;
          videoElement.muted = muted;
          videoElement.load();
        }
      });
    }
  }, [containerRef, autoPlay, muted]);
};

export default useLazyVideo;
