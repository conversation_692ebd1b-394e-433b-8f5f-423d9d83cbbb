// lib/store.ts
import { create } from "zustand";

// Define the interface for typescript
interface CounterState {
  count: number;
}

interface CounterActions {
  increment: () => void;
  decrement: () => void;
}

// Create the store
export const useCounterStore = create<CounterState & CounterActions>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
}));

// example page how to use the store

// import { useCounterStore } from "utils/redux/ex.store";

// export default function Counter() {
//   const { increment, decrement } = useCounterStore();

//   const count = useCounterStore((state) => state.count);

//   return (
//     <div style={{ textAlign: "center", margin: "2rem" }}>
//       <h2>Counter: {count}</h2>
//       <button onClick={increment}>Increment</button>
//       <button onClick={decrement} style={{ marginLeft: "1rem" }}>
//         Decrement
//       </button>
//     </div>
//   );
// }
