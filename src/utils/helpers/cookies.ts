/* eslint-disable @typescript-eslint/no-empty-object-type */
import "server-only";
import { cookies as nextCookies } from "next/headers";

export const getAuthHeaders = async (): Promise<
  { authorization: string } | {}
> => {
  const cookies = await nextCookies();
  const token = cookies.get("_medusa_jwt")?.value;

  if (!token) {
    return {};
  }

  return { authorization: `Bearer ${token}` };
};

export const getCacheTag = async (tag: string): Promise<string> => {
  try {
    return tag;
  } catch (error: unknown) {
    if (error instanceof Error) {
      return error.message;
    }
    return "";
  }
};

export const getCacheOptions = async (
  tag: string
): Promise<{ tags: string[] } | {}> => {
  if (typeof window !== "undefined") {
    return {};
  }

  const cacheTag = await getCacheTag(tag);

  if (!cacheTag) {
    return {};
  }

  return { tags: [`${cacheTag}`] };
};

export const setAuthToken = async (token: string) => {
  const cookies = await nextCookies();
  cookies.set("_medusa_jwt", token, {
    maxAge: 60 * 60 * 24 * 7,
    httpOnly: false,
    sameSite: "strict",
    secure: process.env.NODE_ENV === "production",
  });
};

export const removeAuthToken = async () => {
  const cookies = await nextCookies();
  cookies.set("_medusa_jwt", "", {
    maxAge: -1,
  });
};

export const getCartId = async () => {
  const cookies = await nextCookies();
  return cookies.get("_medusa_cart_id")?.value;
};

export const setCartId = async (cartId: string) => {
  const cookies = await nextCookies();
  cookies.set("_medusa_cart_id", cartId, {
    maxAge: 60 * 60 * 24 * 7,
    httpOnly: true,
    sameSite: "strict",
    secure: process.env.NODE_ENV === "production",
  });
};

export const removeCartId = async () => {
  const cookies = await nextCookies();
  cookies.set("_medusa_cart_id", "", {
    maxAge: -1,
  });
};
export function getCookie(name: string): string | null {
  try {
    const value = document.cookie
      .split("; ")
      .find((row) => row.startsWith(name + "="))
      ?.split("=")[1];

    return value ? decodeURIComponent(value) : null;
  } catch (error) {
    console.error("Error getting cookie:", error);
    return null;
  }
}

interface CookieOptions {
  expires?: Date | number;
  path?: string;
  secure?: boolean;
  sameSite?: "Strict" | "Lax" | "None";
}

export function setCookie(
  name: string,
  value: string,
  options: CookieOptions = {}
): void {
  try {
    let cookieString = `${name}=${encodeURIComponent(value)}`;

    if (options.expires) {
      if (typeof options.expires === "number") {
        const d = new Date();
        d.setDate(d.getDate() + options.expires);
        options.expires = d;
      }
      cookieString += `; expires=${options.expires.toUTCString()}`;
    }

    if (options.path) cookieString += `; path=${options.path}`;
    if (options.secure) cookieString += "; secure";
    if (options.sameSite) cookieString += `; samesite=${options.sameSite}`;

    document.cookie = cookieString;
  } catch (error) {
    console.error("Error setting cookie:", error);
  }
}

export function removeCookie(name: string, path: string = "/"): void {
  try {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;
  } catch (error) {
    console.error("Error removing cookie:", error);
  }
}
