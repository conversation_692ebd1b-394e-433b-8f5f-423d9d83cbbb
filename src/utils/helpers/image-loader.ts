"use client";

export default function myImageLoader({
  src,
  width,
  quality,
}: {
  src: string;
  width: number;
  quality?: number;
}) {
  const baseUrl = process.env.NEXT_PUBLIC_CLOUDFRONT_URL || "";

  // Determine optimized width
  let optimizedWidth = width;
  if (!width) {
    const viewportWidth =
      typeof window !== "undefined" ? window.innerWidth : 1024;

    if (viewportWidth < 400) {
      optimizedWidth = 375;
    } else if (viewportWidth < 600) {
      optimizedWidth = 540;
    } else if (viewportWidth < 800) {
      optimizedWidth = 768;
    } else if (viewportWidth < 1100) {
      optimizedWidth = 1024;
    } else if (viewportWidth < 1600) {
      optimizedWidth = 1440;
    } else {
      optimizedWidth = 1920;
    }
  }

  let finalSrc: string;

  try {
    const originalUrl = new URL(src);

    // If src has a base URL and it doesn't match the CloudFront domain, replace it
    const cloudfrontHost = new URL(baseUrl).host;
    if (originalUrl.host !== cloudfrontHost) {
      originalUrl.host = cloudfrontHost;
      originalUrl.protocol = new URL(baseUrl).protocol;
    }

    originalUrl.searchParams.set("w", optimizedWidth.toString());
    originalUrl.searchParams.set("q", (quality || 70).toString());
    finalSrc = originalUrl.href;
  } catch {
    return src;
  }

  return finalSrc;
}
