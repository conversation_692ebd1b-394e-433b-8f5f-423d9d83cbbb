import { MediaType } from "utils/types/common";

// To get the media type from the url
export const getMediaType = (url?: string | null): MediaType => {
  if (!url) return "image";

  const extension = url.split(".").pop()?.toLowerCase();

  if (extension === "gif") return "gif";
  if (["mp4", "webm", "ogg", "mov"].includes(extension || "")) return "video";
  return "image";
};

// To get the percentage difference between the original and calculated amount
export const getPercentageDiff = (original: number, calculated: number) => {
  const diff = original - calculated;
  const decrease = (diff / original) * 100;

  return decrease.toFixed();
};

/* eslint-disable @typescript-eslint/no-explicit-any */
export const isObject = (input: any) => input instanceof Object;
export const isArray = (input: any) => Array.isArray(input);
export const isEmpty = (input: any) => {
  return (
    input === null ||
    input === undefined ||
    (isObject(input) && Object.keys(input).length === 0) ||
    (isArray(input) && (input as any[]).length === 0) ||
    (typeof input === "string" && input.trim().length === 0)
  );
};

// To convert the amount to the locale currency
type ConvertToLocaleParams = {
  amount: number;
  currency_code?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  locale?: string;
};

export const convertToLocale = ({
  amount,
  currency_code = "INR",
  minimumFractionDigits,
  maximumFractionDigits,
  locale = "en-US",
}: ConvertToLocaleParams) => {
  const isWholeNumber = Number(amount) === Math.floor(Number(amount));
  return currency_code && !isEmpty(currency_code)
    ? new Intl.NumberFormat(locale, {
        style: "currency",
        currency: currency_code,
        minimumFractionDigits: isWholeNumber ? 0 : (minimumFractionDigits ?? 2),
        maximumFractionDigits: isWholeNumber ? 0 : (maximumFractionDigits ?? 2),
      }).format(amount)
    : amount.toString();
};
// end convertToLocale
