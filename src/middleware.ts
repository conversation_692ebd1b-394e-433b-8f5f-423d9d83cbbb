import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  //   // Read the 'isLoggedIn' cookie sent from the browser

  const isLoggedIn = request.cookies.get("isLoggedIn")?.value;

  //   // Allow access to the login route and static assets without checking
  if (
    request.nextUrl.pathname.startsWith("/login") ||
    request.nextUrl.pathname.startsWith("/_next") ||
    request.nextUrl.pathname === "/favicon.ico"
  ) {
    return NextResponse.next();
  }

  // If cookie does not exist, redirect to /login
  if (!isLoggedIn) {
    const loginUrl = request.nextUrl.clone();
    loginUrl.pathname = "/login";
    return NextResponse.redirect(loginUrl);
  }

  // If authenticated, continue as normal
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|favicon.ico|login).*)"],
};
