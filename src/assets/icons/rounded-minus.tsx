import React from "react";

import { IconProps } from "utils/types/common";

function RoundedMinus({ stroke = "#171717", ...attributes }: IconProps) {
  return (
    <svg
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <circle cx="6.5" cy="6.5" r="5.5" stroke={stroke} strokeWidth="0.5" />
      <path
        d="M4 6.5H9"
        stroke={stroke}
        strokeWidth="0.5"
        strokeLinecap="round"
      />
    </svg>
  );
}

export default RoundedMinus;
