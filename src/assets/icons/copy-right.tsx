import React from "react";

import { IconProps } from "utils/types/common";

function CopyRight({ color = "currentColor", ...attributes }: IconProps) {
  return (
    <svg
      width="10"
      height="10"
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M5.28048 8.84124C7.64453 8.84124 9.56096 6.9248 9.56096 4.56075C9.56096 2.19671 7.64453 0.280273 5.28048 0.280273C2.91644 0.280273 1 2.19671 1 4.56075C1 6.9248 2.91644 8.84124 5.28048 8.84124Z"
        stroke={color}
        strokeWidth="0.35"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.70746 5.6303C6.48282 5.9297 6.16968 6.15083 5.81239 6.26239C5.4551 6.37394 5.07177 6.37025 4.71668 6.25186C4.3616 6.13346 4.05277 5.90635 3.83392 5.60269C3.61508 5.29903 3.49732 4.93421 3.49731 4.55991C3.49731 4.18561 3.61507 3.82079 3.83391 3.51713C4.05275 3.21347 4.36158 2.98636 4.71666 2.86795C5.07174 2.74955 5.45508 2.74586 5.81237 2.85741C6.16966 2.96896 6.48281 3.19009 6.70745 3.48948"
        stroke={color}
        strokeWidth="0.35"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default CopyRight;
