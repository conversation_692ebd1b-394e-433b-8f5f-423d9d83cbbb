import React from "react";

import { IconProps } from "utils/types/common";

function Bag({ color = "currentColor", ...attributes }: IconProps) {
  return (
    <svg
      width="18"
      height="25"
      viewBox="0 0 18 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M6.30249 4.93975V3.94786C6.30249 2.32031 7.6228 1 9.25035 1C10.8779 1 12.1982 2.31899 12.1982 3.94786V4.93975"
        fill={"none"}
        stroke={color}
        strokeWidth="0.8"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
      <path
        d="M15.5724 24H2.92815C1.80781 24 0.923182 23.1048 1.00529 22.0546L2.04485 8.83161C2.11901 7.88607 2.95729 7.15374 3.96772 7.15374H14.5329C15.542 7.15374 16.3816 7.88607 16.4557 8.83161L17.4953 22.0546C17.5774 23.1048 16.6928 24 15.5724 24Z"
        fill={"none"}
        stroke={color}
        strokeWidth="0.8"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
    </svg>
  );
}

export default Bag;
