import React from "react";

import { IconProps } from "utils/types/common";

const ArrowLeft = ({ color = "currentColor", ...attributes }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="13"
      height="22"
      viewBox="0 0 13 22"
      fill="none"
      {...attributes}
    >
      <path
        d="M11.0603 21L12 20.0659L2.8794 11L12 1.93407L11.0603 1L1 11L11.0603 21Z"
        fill={color}
        stroke={color}
        strokeWidth="0.5"
      />
    </svg>
  );
};

export default ArrowLeft;
