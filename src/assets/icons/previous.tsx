import React from "react";

import { IconProps } from "utils/types/common";

const Previous = ({ color = "currentColor", ...attributes }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="17"
      height="6"
      viewBox="0 0 17 6"
      fill="none"
      {...attributes}
    >
      <path
        d="M0.734835 2.73484C0.588388 2.88128 0.588388 3.11872 0.734835 3.26517L3.12132 5.65165C3.26777 5.7981 3.5052 5.7981 3.65165 5.65165C3.7981 5.5052 3.7981 5.26777 3.65165 5.12132L1.53033 3L3.65165 0.878681C3.7981 0.732234 3.7981 0.494797 3.65165 0.348351C3.5052 0.201904 3.26777 0.201904 3.12132 0.348351L0.734835 2.73484ZM17 2.625L1 2.625L1 3.375L17 3.375L17 2.625Z"
        fill={color}
      />
    </svg>
  );
};

export default Previous;
