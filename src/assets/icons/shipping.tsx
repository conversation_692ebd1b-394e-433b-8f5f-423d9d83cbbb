import React from "react";

import { IconProps } from "utils/types/common";

const Shipping = ({ color = "currentColor", ...attributes }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="35"
      height="27"
      viewBox="0 0 35 27"
      fill="none"
      {...attributes}
    >
      <path
        d="M24.4919 20.96H12.5519C12.2204 20.96 11.9024 20.8283 11.668 20.5939C11.4336 20.3595 11.3019 20.0415 11.3019 19.71C11.3019 19.3785 11.4336 19.0605 11.668 18.8261C11.9024 18.5917 12.2204 18.46 12.5519 18.46H24.4919C24.8234 18.46 25.1413 18.5917 25.3758 18.8261C25.6102 19.0605 25.7419 19.3785 25.7419 19.71C25.7419 20.0415 25.6102 20.3595 25.3758 20.5939C25.1413 20.8283 24.8234 20.96 24.4919 20.96ZM31.3719 20.96H31.0719C30.7404 20.96 30.4224 20.8283 30.188 20.5939C29.9536 20.3595 29.8219 20.0415 29.8219 19.71C29.8219 19.3785 29.9536 19.0605 30.188 18.8261C30.4224 18.5917 30.7404 18.46 31.0719 18.46H31.3719C31.4541 18.4613 31.5358 18.4463 31.6122 18.4157C31.6886 18.3851 31.7581 18.3397 31.8168 18.282C31.8754 18.2243 31.922 18.1555 31.9537 18.0796C31.9855 18.0037 32.0019 17.9223 32.0019 17.84V13.55C32.0015 13.4093 31.954 13.2728 31.867 13.1623C31.78 13.0517 31.6585 12.9735 31.5219 12.94H24.1519C23.3235 12.94 22.5289 12.6116 21.9422 12.0268C21.3555 11.442 21.0245 10.6484 21.0219 9.82V3.12C21.0232 3.03821 21.0081 2.95698 20.9774 2.88115C20.9468 2.80531 20.9011 2.73643 20.8433 2.67858C20.7854 2.62074 20.7166 2.57512 20.6407 2.54444C20.5649 2.51376 20.4837 2.49865 20.4019 2.5H5.67188C5.34035 2.5 5.02241 2.3683 4.78799 2.13388C4.55357 1.89946 4.42188 1.58152 4.42188 1.25C4.42188 0.918479 4.55357 0.600537 4.78799 0.366117C5.02241 0.131696 5.34035 0 5.67188 0H20.4019C20.8116 0 21.2173 0.0807012 21.5958 0.237496C21.9744 0.394291 22.3183 0.624108 22.608 0.913827C22.8978 1.20355 23.1276 1.54749 23.2844 1.92603C23.4412 2.30456 23.5219 2.71028 23.5219 3.12V9.8C23.5219 9.88227 23.5382 9.96372 23.57 10.0396C23.6018 10.1155 23.6484 10.1843 23.707 10.242C23.7656 10.2997 23.8352 10.3451 23.9115 10.3757C23.9879 10.4063 24.0696 10.4213 24.1519 10.42H31.3719C31.6002 10.423 31.8278 10.4464 32.0519 10.49L32.2719 10.56C32.9194 10.7493 33.4875 11.1447 33.8899 11.6861C34.2924 12.2275 34.5072 12.8854 34.5019 13.56V17.85C34.4966 18.6767 34.1645 19.4677 33.5781 20.0503C32.9917 20.633 32.1985 20.96 31.3719 20.96Z"
        fill={color}
      />
      <path
        d="M31.7992 12.98C31.5422 12.98 31.2916 12.9001 31.082 12.7514C30.8724 12.6027 30.7142 12.3926 30.6292 12.15L28.6292 6.59003C28.5754 6.515 28.505 6.45341 28.4235 6.41005C28.3419 6.36669 28.2515 6.34272 28.1592 6.34003H22.2492C21.9177 6.34003 21.5998 6.20833 21.3653 5.97391C21.1309 5.73949 20.9992 5.42155 20.9992 5.09003C20.9992 4.75851 21.1309 4.44056 21.3653 4.20614C21.5998 3.97172 21.9177 3.84003 22.2492 3.84003H28.1592C28.7134 3.84218 29.2567 3.99381 29.7319 4.27893C30.2071 4.56404 30.5965 4.97208 30.8592 5.46003C30.89 5.5181 30.9168 5.57825 30.9392 5.64003L32.9392 11.31C33.0486 11.6231 33.0301 11.9666 32.8878 12.2661C32.7455 12.5656 32.4909 12.797 32.1792 12.91C32.056 12.9492 31.9283 12.9727 31.7992 12.98ZM27.7792 26.3C26.8793 26.3 25.9996 26.0332 25.2514 25.5332C24.5031 25.0333 23.9199 24.3226 23.5756 23.4912C23.2312 22.6598 23.1411 21.745 23.3166 20.8624C23.4922 19.9798 23.9256 19.169 24.5619 18.5327C25.1982 17.8964 26.0089 17.463 26.8916 17.2875C27.7742 17.1119 28.689 17.202 29.5204 17.5464C30.3518 17.8908 31.0624 18.4739 31.5624 19.2222C32.0624 19.9704 32.3292 20.8501 32.3292 21.75C32.3292 22.9568 31.8498 24.1141 30.9966 24.9674C30.1433 25.8207 28.986 26.3 27.7792 26.3ZM27.7792 19.7C27.3718 19.6901 26.9706 19.8018 26.627 20.0209C26.2834 20.2401 26.0129 20.5567 25.85 20.9303C25.6871 21.3039 25.6393 21.7175 25.7127 22.1184C25.7861 22.5193 25.9773 22.8893 26.2619 23.181C26.5465 23.4727 26.9116 23.673 27.3105 23.7562C27.7095 23.8394 28.1242 23.8019 28.5017 23.6483C28.8792 23.4947 29.2024 23.2321 29.43 22.894C29.6575 22.5559 29.7791 22.1576 29.7792 21.75C29.7794 21.2149 29.5703 20.7009 29.1966 20.3179C28.8229 19.9348 28.3142 19.7131 27.7792 19.7ZM9.24922 26.3C8.34932 26.3 7.46962 26.0332 6.72138 25.5332C5.97313 25.0333 5.38995 24.3226 5.04557 23.4912C4.70119 22.6598 4.61109 21.745 4.78665 20.8624C4.96221 19.9798 5.39556 19.169 6.03189 18.5327C6.66821 17.8964 7.47895 17.463 8.36156 17.2875C9.24417 17.1119 10.159 17.202 10.9904 17.5464C11.8218 17.8908 12.5324 18.4739 13.0324 19.2222C13.5324 19.9704 13.7992 20.8501 13.7992 21.75C13.7992 22.9568 13.3198 24.1141 12.4666 24.9674C11.6133 25.8207 10.456 26.3 9.24922 26.3ZM9.24922 19.7C8.84377 19.7 8.44742 19.8203 8.1103 20.0455C7.77318 20.2708 7.51043 20.5909 7.35527 20.9655C7.20011 21.3401 7.15951 21.7523 7.23861 22.15C7.31771 22.5476 7.51295 22.9129 7.79965 23.1996C8.08635 23.4863 8.45163 23.6815 8.84929 23.7606C9.24695 23.8397 9.65913 23.7991 10.0337 23.644C10.4083 23.4888 10.7285 23.2261 10.9537 22.8889C11.179 22.5518 11.2992 22.1555 11.2992 21.75C11.2992 21.2063 11.0832 20.6849 10.6988 20.3005C10.3143 19.916 9.79291 19.7 9.24922 19.7Z"
        fill={color}
      />
      <path
        d="M22.25 20.96C22.0855 20.9613 21.9223 20.9299 21.7701 20.8676C21.6178 20.8052 21.4795 20.7132 21.3631 20.5969C21.2468 20.4805 21.1548 20.3422 21.0924 20.1899C21.0301 20.0377 20.9987 19.8745 21 19.71V8.78001C21 8.44849 21.1317 8.13054 21.3661 7.89612C21.6005 7.6617 21.9185 7.53001 22.25 7.53001C22.5815 7.53001 22.8995 7.6617 23.1339 7.89612C23.3683 8.13054 23.5 8.44849 23.5 8.78001V19.71C23.5013 19.8745 23.4699 20.0377 23.4076 20.1899C23.3452 20.3422 23.2532 20.4805 23.1369 20.5969C23.0205 20.7132 22.8822 20.8052 22.7299 20.8676C22.5777 20.9299 22.4145 20.9613 22.25 20.96ZM9.44 8.82001H1.25C0.918479 8.82001 0.600537 8.68831 0.366117 8.45389C0.131696 8.21947 0 7.90153 0 7.57001C0 7.23849 0.131696 6.92054 0.366117 6.68612C0.600537 6.4517 0.918479 6.32001 1.25 6.32001H9.44C9.77152 6.32001 10.0895 6.4517 10.3239 6.68612C10.5583 6.92054 10.69 7.23849 10.69 7.57001C10.69 7.90153 10.5583 8.21947 10.3239 8.45389C10.0895 8.68831 9.77152 8.82001 9.44 8.82001ZM10.98 14.4H6.7C6.36848 14.4 6.05054 14.2683 5.81612 14.0339C5.5817 13.7995 5.45 13.4815 5.45 13.15C5.45 12.8185 5.5817 12.5005 5.81612 12.2661C6.05054 12.0317 6.36848 11.9 6.7 11.9H10.98C11.3115 11.9 11.6295 12.0317 11.8639 12.2661C12.0983 12.5005 12.23 12.8185 12.23 13.15C12.23 13.4815 12.0983 13.7995 11.8639 14.0339C11.6295 14.2683 11.3115 14.4 10.98 14.4Z"
        fill={color}
      />
    </svg>
  );
};

export default Shipping;
