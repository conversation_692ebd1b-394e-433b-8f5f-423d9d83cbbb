import React from "react";

import { IconProps } from "utils/types/common";

function RoundedPlus({ stroke = "#171717", ...attributes }: IconProps) {
  return (
    <svg
      width="13"
      height="13"
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M6.49968 9C6.46742 9 6.44048 8.98912 6.41883 8.96737C6.39719 8.94556 6.38636 8.91856 6.38636 8.88636V6.61364H4.11364C4.08144 6.61364 4.05444 6.60271 4.03263 6.58084C4.01088 6.55904 4 6.53198 4 6.49968C4 6.46742 4.01088 6.44048 4.03263 6.41883C4.05444 6.39719 4.08144 6.38636 4.11364 6.38636H6.38636V4.11364C6.38636 4.08144 6.39729 4.05446 6.41916 4.03271C6.44096 4.0109 6.46802 4 6.50032 4C6.53258 4 6.55952 4.0109 6.58117 4.03271C6.60281 4.05446 6.61364 4.08144 6.61364 4.11364V6.38636H8.88636C8.91856 6.38636 8.94554 6.39729 8.96729 6.41916C8.9891 6.44096 9 6.46802 9 6.50032C9 6.53258 8.9891 6.55952 8.96729 6.58117C8.94554 6.60281 8.91856 6.61364 8.88636 6.61364H6.61364V8.88636C6.61364 8.91856 6.60271 8.94556 6.58084 8.96737C6.55904 8.98912 6.53198 9 6.49968 9Z"
        fill="#171717"
        stroke={stroke}
        strokeWidth="0.25"
      />
      <circle cx="6.5" cy="6.5" r="5.5" stroke={stroke} strokeWidth="0.5" />
    </svg>
  );
}

export default RoundedPlus;
