import React from "react";

import { IconProps } from "utils/types/common";

function Search({ color = "currentColor", ...attributes }: IconProps) {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M8.66947 16.3389C12.9052 16.3389 16.3389 12.9052 16.3389 8.66947C16.3389 4.43374 12.9052 1 8.66947 1C4.43374 1 1 4.43374 1 8.66947C1 12.9052 4.43374 16.3389 8.66947 16.3389Z"
        stroke={color}
        strokeWidth="0.8"
        strokeMiterlimit="10"
        strokeLinecap="round"
        color={color}
      />
      <path
        d="M21.0001 21L14.0884 14.0883"
        stroke={color}
        strokeWidth="0.8"
        color={color}
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
    </svg>
  );
}

export default Search;
