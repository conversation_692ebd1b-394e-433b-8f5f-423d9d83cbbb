import React from "react";

import { IconProps } from "utils/types/common";

function UserOutlined({ color = "currentColor", ...attributes }: IconProps) {
  return (
    <svg
      width="17"
      height="24"
      viewBox="0 0 17 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M8.29817 10.9225C11.0382 10.9225 13.2594 8.70125 13.2594 5.96122C13.2594 3.2212 11.0382 0.999969 8.29817 0.999969C5.55814 0.999969 3.33691 3.2212 3.33691 5.96122C3.33691 8.70125 5.55814 10.9225 8.29817 10.9225Z"
        fill={"none"}
        stroke={color}
        strokeWidth="0.8"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
      <path
        d="M1 23.4021V20.8273C1 16.5996 4.42725 13.1723 8.655 13.1723C12.8827 13.1723 16.31 16.5996 16.31 20.8273V23.4021"
        fill={"none"}
        stroke={color}
        strokeWidth="0.8"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
    </svg>
  );
}

export default UserOutlined;
