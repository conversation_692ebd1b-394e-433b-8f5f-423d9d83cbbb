@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .non-selectable {
    user-select: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
  }

  /* custom styling */
  button {
    box-shadow: none !important;
    @apply cta-copy;
  }
}

@layer base {
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"] {
    margin: 0;
    -webkit-appearance: none;
    appearance: textfield;
    -moz-appearance: textfield !important;
  }
  input[type="date"] {
    -webkit-appearance: none;
    appearance: none;
    height: 60px;
  }

  input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
  }

  input[type="date"]::-webkit-date-and-time-value {
    text-align: left;
  }

  :root {
    /* text colors */
    --text-primary: #171717;
    --text-secondary: #5d5d5d;
    --text-contrast: #ffffff;
    /* background colors */
    --bg-primary: #ffffff;
    --bg-secondary: #000000;
    --bg-accent: #aa182c;
    --bg-container: #eaeaea;
    --bg-cards: #f4f4f4;

    /* card colors */
    --muted: #eaeaea;

    /* border colors */
    --border: #171717;

    /* validations */
    --validations-success: #2acf37;
    --validations-error: #aa182c;
    --validations-warning: #ff9800;
  }
  .dark {
    /* custom text colors */
    /* text colors */
    --text-primary: #171717;
    --text-secondary: #5d5d5d;
    --text-contrast: #ffffff;
    /* background colors */
    --bg-primary: #ffffff;
    --bg-secondary: #000000;
    --bg-accent: #aa182c;
    --bg-container: #eaeaea;
    --bg-cards: #f4f4f4;

    /* card colors */
    --muted: #eaeaea;

    /* border colors */
    --border: #171717;

    /* validations */
    --validations-success: #2acf37;
    --validations-error: #aa182c;
    --validations-warning: #ff9800;
  }
}

@layer base {
  * {
    @apply border-theme-other-border;
    scroll-behavior: smooth;
    scroll-padding-top: 80px; /* Add padding for sticky header */
  }
  body {
    @apply bg-theme-background-primary text-theme-text-primary;
  }
}
