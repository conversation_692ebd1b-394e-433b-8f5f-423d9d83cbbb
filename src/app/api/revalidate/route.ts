import { revalidateTag, revalidatePath } from "next/cache";

export async function POST(req: Request) {
  try {
    const body = await req.json();

    if (process.env.REVALIDATE_SECRET === body.secret) {
      // Tag-based revalidation
      if (Array.isArray(body.tags)) {
        body.tags.map((tag: string) => {
          revalidateTag(tag);
        });
      }
      // Path-based revalidation
      if (typeof body.path === "string") {
        revalidatePath(body.path);
      }
      return new Response("Revalidation triggered", {
        status: 200,
        statusText: "OK",
      });
    } else {
      return new Response("Unauthorized", { status: 401 });
    }
  } catch (error: Error | unknown) {
    console.error("Error: ", error);
    return new Response(
      `Error: ${error instanceof Error ? error.message : String(error)}`,
      { status: 500 }
    );
  }
}
