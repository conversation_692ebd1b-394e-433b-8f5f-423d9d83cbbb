"use client";

import React, { useState } from "react";

import { useRouter } from "next/navigation";

import Button from "@modules/ui-elements/button";
import Input from "@modules/ui-elements/input";

import { AUTH_CREDS } from "./data";

function LoginPage() {
  const [isLoginLoading, setIsLoginLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const [state, setState] = useState({
    email: "",
    password: "",
  });

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoginLoading(true);
    setError("");

    if (
      state.email !== AUTH_CREDS.email ||
      state.password !== AUTH_CREDS.password
    ) {
      setIsLoginLoading(false);
      setError("Invalid email or password");
      return;
    }

    setTimeout(() => {
      setIsLoginLoading(false);
      setCookie("isLoggedIn", "true", 3);
      router.push("/");
    }, 1000);
  };

  return (
    <div className="flex h-screen w-full items-center justify-center p-20">
      <form className="w-1/2" onSubmit={handleLogin}>
        <div className="mb-5">
          <label className="mb-2 block text-sm font-medium">Your email</label>
          <Input
            type="email"
            id="email"
            className="block w-full rounded-lg"
            placeholder="email"
            required
            onChange={(e) => setState({ ...state, email: e.target.value })}
            value={state.email}
          />
        </div>
        <div className="mb-5">
          <label className="mb-2 block text-sm font-medium">
            Your password
          </label>
          <Input
            type="password"
            id="password"
            className="block w-full rounded-lg text-sm"
            required
            onChange={(e) => setState({ ...state, password: e.target.value })}
            value={state.password}
          />
        </div>
        {error && <div className="mb-5 text-sm text-red-500">{error}</div>}
        <Button
          type="submit"
          loading={isLoginLoading}
          className="w-full rounded-lg px-5 py-2.5 text-center text-sm font-medium"
        >
          Submit
        </Button>
      </form>
    </div>
  );
}

export default LoginPage;

const setCookie = (name: string, value: string, days: number) => {
  const expires = new Date(Date.now() + days * 86400000).toUTCString();
  document.cookie = `${name}=${value}; expires=${expires}; path=/`;
};
