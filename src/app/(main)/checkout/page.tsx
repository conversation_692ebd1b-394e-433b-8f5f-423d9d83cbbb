import React from "react";

import { Metadata } from "next";

import { notFound } from "next/navigation";

import Checkout from "@modules/checkout";
import { retrieveCart } from "utils/api/server-api/cart";
import { retrieveCustomer } from "utils/api/server-api/customer";

export const metadata: Metadata = {
  title: "Checkout",
};

const CheckoutPage = async () => {
  const cart = await retrieveCart();

  if (!cart) {
    return notFound();
  }

  const customer = await retrieveCustomer();

  return <Checkout cart={cart} customer={customer} />;
};

export default CheckoutPage;
