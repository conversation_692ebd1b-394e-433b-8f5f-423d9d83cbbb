import React from "react";

export default function Loading() {
  return (
    <div className="space-y-[52px] md:space-y-[72px] lg:space-y-[120px]">
      <div className="space-y-5">
        <div className="my-5 px-2 md-container">
          <div className="flex items-center gap-2">
            <div className="h-10 w-1/3 animate-pulse rounded bg-gray-200" />
          </div>
        </div>

        <div className="relative h-[721px] w-full animate-pulse rounded-lg bg-gray-100" />
        <div className="flex w-full flex-col items-center justify-center gap-10 px-5 py-5 lg:flex-row lg:items-start lg:px-0 lg:py-20">
          <div className="h-[564px] w-full animate-pulse rounded-lg bg-gray-100 lg:w-[459px]" />
          <div className="w-full lg:w-[382px]">
            <div className="h-[420px] w-full animate-pulse rounded bg-gray-200" />
          </div>
        </div>

        <div className="flex w-full flex-col items-center justify-center py-10">
          <div className="w-1/2 animate-pulse rounded-lg bg-gray-100 pb-14 pt-14 lg:w-full lg:pt-32"></div>
          <div className="flex w-full items-start justify-center gap-10 px-5 lg:px-20">
            {Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="h-[420px] w-full animate-pulse rounded-lg bg-gray-100 lg:w-[382px]"
              />
            ))}
          </div>
        </div>
        <div className="mt-[79px] md:mt-[83px]">
          <div className="h-screen w-full animate-pulse rounded bg-gray-200" />
        </div>
      </div>
    </div>
  );
}
