import React from "react";

export default function Loading() {
  return (
    <div className="flex w-full flex-col items-center justify-center py-20">
      <div
        className={`flex h-[200px] w-full flex-col items-center justify-start gap-3`}
      >
        <div className="h-14 w-[140px] animate-pulse rounded bg-gray-200" />
        <div className="h-14 w-[400px] animate-pulse rounded bg-gray-200" />
      </div>
      <div className="grid w-full grid-cols-3 items-start justify-center gap-5 px-5">
        {Array.from({ length: 6 }).map((_, index) => {
          return (
            <div
              className="group flex w-full cursor-pointer items-center justify-center"
              key={index}
            >
              <div className="flex w-[420px] flex-col items-start justify-start gap-y-2">
                <div className="flex w-full items-center justify-start text-center">
                  <div className="h-[12px] w-[100px] animate-pulse rounded bg-gray-200" />
                </div>
                <div className="h-[226px] w-full animate-pulse rounded bg-gray-200" />
                <div className="flex w-full flex-col items-start justify-start gap-4 text-center">
                  <div className="h-10 w-full animate-pulse rounded bg-gray-200" />
                  <div className="h-10 w-[120px] animate-pulse rounded bg-gray-200" />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
