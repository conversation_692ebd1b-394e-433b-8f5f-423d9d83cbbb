import { Metadata } from "next";

import Cart from "@modules/cart";
import { retrieveCart } from "utils/api/server-api/cart";
import { retrieveCustomer } from "utils/api/server-api/customer";

export const metadata: Metadata = {
  title: "Cart",
  description: "View your cart",
};

const CartPage = async () => {
  const cart = await retrieveCart();
  const customer = await retrieveCustomer();

  // if (!cart) {
  //   return notFound();
  // }

  return <Cart cart={cart} customer={customer} />;
};

export default CartPage;
