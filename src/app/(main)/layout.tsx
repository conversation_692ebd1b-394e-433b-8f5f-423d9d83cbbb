import { Metadata } from "next";

import BaseLayout from "@modules/layouts/base-layout";
import { config } from "utils/config";
import { brand_info } from "utils/constants";

export const metadata: Metadata = {
  metadataBase: new URL(config.NEXT_PUBLIC_BASE_URL),
  description: brand_info.description,
};

export default async function PageLayout(props: { children: React.ReactNode }) {
  return <BaseLayout>{props.children}</BaseLayout>;
}
