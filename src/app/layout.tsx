import type { Metadata } from "next";

import localFont from "next/font/local";

import { config } from "utils/config";
import { brand_info } from "utils/constants";

import "./globals.css";

const acumin = localFont({
  src: [
    {
      path: "./fonts/acumin/acumin-variable-concept-bold.woff2",
      weight: "100 900",
    },
  ],
  variable: "--font-acumin",
  display: "swap",
});

const montserrat = localFont({
  src: [
    {
      path: "./fonts/montserrat/montserrat-variable-font-wght.ttf",
      weight: "100 900",
      style: "normal",
    },
  ],
  variable: "--font-montserrat",
  display: "swap",
});

export const metadata: Metadata = {
  metadataBase: new URL(config.NEXT_PUBLIC_BASE_URL),
  title: {
    default: brand_info.name,
    template: `%s | ${brand_info.name}`,
  },
  description: brand_info.description,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${acumin.variable} ${montserrat.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
