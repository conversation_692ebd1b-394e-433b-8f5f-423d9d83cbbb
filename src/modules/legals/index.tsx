import React from "react";

import { notFound } from "next/navigation";

import { getPageTemplateName } from "utils/api/strapi-api";

import DynamicLegalPage from "./components";

async function LegalModule({ slug }: { slug: string }) {
  const [{ template_name, page_type }] = await Promise.all([
    getPageTemplateName(slug),
  ]);

  if (!template_name || !page_type) {
    console.warn(`-> No template name found for page: ${slug}`);
    notFound();
  }
  return (
    <DynamicLegalPage
      page={slug}
      page_type={page_type}
      template_name={template_name}
    />
  );
}

export default LegalModule;
