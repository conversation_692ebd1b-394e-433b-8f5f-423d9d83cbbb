"use client";

import React, { useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import Divider from "@modules/common/divider";
import Button from "@modules/ui-elements/button";
import { Checkbox } from "@modules/ui-elements/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@modules/ui-elements/form";
import Text from "@modules/ui-elements/text";
import { motion, AnimatePresence } from "framer-motion";
import { useForm } from "react-hook-form";
import { handleOrder } from "utils/api/server-api/cart";
import { z } from "zod";

import BillingAddress from "./components/billing-address";
import ShippingAddress from "./components/shipping-address";

export const addressSchema = z
  .object({
    shipping_address: z.object({
      title: z.string().min(1, "Title is required"),
      firstName: z.string().min(1, "First name is required"),
      lastName: z.string().min(1, "Last name is required"),
      addressLine1: z.string().min(1, "Address line 1 is required"),
      addressLine2: z.string(),
      city: z.string().min(1, "City is required"),
      postcode: z.string().min(1, "Postcode is required"),
      state: z.string().min(1, "State is required"),
      country: z.string().min(1, "Country is required"),
      phone: z.string().min(1, "Phone number is required"),
    }),
    sameAsShipping: z.boolean(),
    billing_address: z
      .object({
        title: z.string(),
        firstName: z.string(),
        lastName: z.string(),
        addressLine1: z.string(),
        addressLine2: z.string(),
        city: z.string(),
        postcode: z.string(),
        state: z.string(),
        country: z.string(),
        phone: z.string(),
      })
      .optional(),
  })
  .superRefine((data, ctx) => {
    // If sameAsShipping is false, validate that billing_address is provided and complete
    if (!data.sameAsShipping) {
      if (!data.billing_address) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            "Billing address is required when not using shipping address",
          path: ["billing_address"],
        });
        return;
      }

      const billingFields = [
        "title",
        "firstName",
        "lastName",
        "addressLine1",
        "city",
        "postcode",
        "state",
        "country",
        "phone",
      ];

      for (const field of billingFields) {
        if (!data.billing_address[field as keyof typeof data.billing_address]) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `${field.charAt(0).toUpperCase() + field.slice(1)} is required`,
            path: ["billing_address", field],
          });
        }
      }
    }
  });

const AddressStep = () => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      shipping_address: {
        title: "",
        firstName: "",
        lastName: "",
        addressLine1: "",
        addressLine2: "",
        city: "",
        postcode: "",
        state: "",
        country: "",
        phone: "",
      },
      billing_address: {
        title: "",
        firstName: "",
        lastName: "",
        addressLine1: "",
        addressLine2: "",
        city: "",
        postcode: "",
        state: "",
        country: "",
        phone: "",
      },
      sameAsShipping: true,
    },
  });

  const onSubmit = async (data: z.infer<typeof addressSchema>) => {
    try {
      setIsLoading(true);
      await handleOrder(data);
    } catch (error) {
      console.error(error);
    } finally {
      console.log("finally");
      setIsLoading(false);
    }
  };

  return (
    <div id="address-step">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} noValidate>
          <Text as="h2" size="headline" className="leading-[100%]">
            2. SHIPPING ADDRESS
          </Text>
          <Divider className="mb-0 mt-3 border-theme-text-secondary" />
          <Text as="p" size="body_large" className="mt-7">
            Required fields*
          </Text>
          <ShippingAddress form={form} />
          <div className="mt-[26px] flex items-center gap-2">
            <FormField
              control={form.control}
              name="sameAsShipping"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Checkbox
                      id="use-billing-address"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <Text
              as="label"
              htmlFor="use-billing-address"
              size="body_medium"
              className="select-none leading-[100%]"
            >
              Use shipping address for billing address
            </Text>
          </div>
          <AnimatePresence>
            {!form.watch("sameAsShipping") && (
              <motion.div
                initial={{ opacity: 0, height: 0, overflow: "hidden" }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                <BillingAddress form={form} />
              </motion.div>
            )}
          </AnimatePresence>
          <Button
            type="submit"
            className="mt-9"
            theme="primary"
            loading={isLoading}
            disabled={isLoading}
          >
            Place Order
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default AddressStep;
