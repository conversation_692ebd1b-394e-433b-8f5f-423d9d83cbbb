import React from "react";

import { Form<PERSON>ield, FormItem, FormControl } from "@modules/ui-elements/form";
import Input from "@modules/ui-elements/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@modules/ui-elements/select";
import Text from "@modules/ui-elements/text";
import { UseFormReturn } from "react-hook-form";
import cn from "utils/helpers/cn";

import { COUNTRIES_OPTIONS, STATES_OPTIONS } from "./utils/data";
import { AddressesFormSchema } from "../../utils/types";

const ShippingAddress = ({
  form,
}: {
  form: UseFormReturn<AddressesFormSchema>;
}) => {
  return (
    <div className="mt-5 grid grid-cols-2 gap-x-5 gap-y-[30px] md:mt-10">
      <div className="col-span-full">
        <FormField
          control={form.control}
          name="shipping_address.title"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  variant="outlined"
                  label="Title"
                  error={
                    form.formState.errors?.shipping_address?.title?.message
                  }
                  {...field}
                  required
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
      <FormField
        control={form.control}
        name="shipping_address.firstName"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Input
                variant="outlined"
                label="First Name"
                error={
                  form.formState.errors?.shipping_address?.firstName?.message
                }
                {...field}
                required
              />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="shipping_address.lastName"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Input
                variant="outlined"
                label="Last Name"
                error={
                  form.formState.errors?.shipping_address?.lastName?.message
                }
                {...field}
                required
              />
            </FormControl>
          </FormItem>
        )}
      />
      <div className="col-span-full">
        <FormField
          control={form.control}
          name="shipping_address.addressLine1"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  variant="outlined"
                  label="Address line 1"
                  error={
                    form.formState.errors?.shipping_address?.addressLine1
                      ?.message
                  }
                  {...field}
                  required
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
      <div className="col-span-full">
        <FormField
          control={form.control}
          name="shipping_address.addressLine2"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  variant="outlined"
                  label="Address line 2"
                  error={
                    form.formState.errors?.shipping_address?.addressLine2
                      ?.message
                  }
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
      <FormField
        control={form.control}
        name="shipping_address.city"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Input
                variant="outlined"
                label="City"
                error={form.formState.errors?.shipping_address?.city?.message}
                {...field}
                required
              />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="shipping_address.postcode"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Input
                variant="outlined"
                label="Postcode"
                error={
                  form.formState.errors?.shipping_address?.postcode?.message
                }
                {...field}
                required
              />
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="shipping_address.country"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <div className="flex flex-col gap-1">
                <Text
                  as="label"
                  size="body_large"
                  className={cn(
                    "mb-1 leading-[100%] text-theme-text-primary",
                    form.formState.errors?.shipping_address?.country?.message &&
                      "text-theme-validations-error"
                  )}
                >
                  Country/Region
                  <span className="ml-px">*</span>
                </Text>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  required
                >
                  <SelectTrigger className="h-full w-full capitalize">
                    <SelectValue placeholder="Select Country" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Select Country</SelectLabel>
                      {COUNTRIES_OPTIONS.map((country) => (
                        <SelectItem key={country.id} value={country.value}>
                          {country.title}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="shipping_address.state"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <div className="flex flex-col gap-1">
                <Text
                  as="label"
                  size="body_large"
                  className={cn(
                    "mb-1 leading-[100%] text-theme-text-primary",
                    form.formState.errors?.shipping_address?.state?.message &&
                      "text-theme-validations-error"
                  )}
                >
                  State
                  <span className="ml-px">*</span>
                </Text>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                  required
                >
                  <SelectTrigger className="h-full w-full capitalize">
                    <SelectValue placeholder="Select State" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[300px]">
                    <SelectGroup>
                      <SelectLabel>Select State</SelectLabel>
                      {STATES_OPTIONS.map((state) => (
                        <SelectItem key={state.value} value={state.value}>
                          {state.title}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </FormControl>
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="shipping_address.phone"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Input
                variant="outlined"
                label="Phone Number"
                error={form.formState.errors?.shipping_address?.phone?.message}
                {...field}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "").slice(0, 10);
                  if (value.length <= 10) {
                    e.target.value = value;
                    field.onChange(value);
                  }
                }}
                required
              />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
};

export default ShippingAddress;
