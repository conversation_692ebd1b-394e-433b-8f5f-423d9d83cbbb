"use client";

import React, { useState } from "react";

import { useRouter } from "next/navigation";

import { zodResolver } from "@hookform/resolvers/zod";
import { HttpTypes } from "@medusajs/types";
import Divider from "@modules/common/divider";
import Button from "@modules/ui-elements/button";
import { Checkbox } from "@modules/ui-elements/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@modules/ui-elements/form";
import Input from "@modules/ui-elements/input";
import Text from "@modules/ui-elements/text";
import { useForm } from "react-hook-form";
import { updateCart } from "utils/api/server-api/cart";
import { z } from "zod";

const identificationSchema = z.object({
  email: z.string().min(1, "Email is required").email("Invalid email address"),
  termsAndConditions: z.boolean().refine((val) => val, {
    message: "You must agree to the terms and conditions",
  }),
});

const IdentificationStep = ({ cart }: { cart: HttpTypes.StoreCart }) => {
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(identificationSchema),
    defaultValues: {
      email: cart.email || "",
      termsAndConditions: cart.email ? true : false,
    },
  });

  const onSubmit = async (data: z.infer<typeof identificationSchema>) => {
    try {
      setIsLoading(true);
      const cart = await updateCart({
        email: data.email,
      });

      router.push("/checkout#address-step");
      console.log({ cart });
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Text as="h2" size="headline" className="leading-[100%]">
        1. IDENTIFICATION
      </Text>
      <Divider className="mb-0 mt-3 border-theme-text-secondary" />
      <Text as="p" size="body_large" className="mt-7">
        Required fields*
      </Text>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="mt-5 grid grid-cols-1 gap-x-5 gap-y-[30px] md:mt-10">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      variant="outlined"
                      label="Email"
                      error={form.formState.errors?.email?.message}
                      {...field}
                      required
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="mt-[26px] flex items-center gap-2">
              <FormField
                control={form.control}
                name="termsAndConditions"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Checkbox
                        id="terms-and-conditions"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <Text
                as="label"
                size="body_medium"
                htmlFor="terms-and-conditions"
                className="select-none leading-[100%]"
              >
                I have read and understood the Privacy Policy, and I agree to
                receive marketing communications via email.
              </Text>
            </div>
            <div className="flex w-full justify-end">
              <div className="w-full space-y-2.5 md:w-[278px]">
                <Button
                  type="submit"
                  className="mt-9"
                  theme="primary"
                  loading={isLoading}
                  disabled={
                    isLoading ||
                    !form.watch("termsAndConditions") ||
                    form.watch("email") === cart?.email
                  }
                >
                  CONTINUE AS A GUEST
                </Button>
                <Text
                  as="p"
                  size="body_medium"
                  className="text-theme-text-secondary"
                >
                  (Please note that if the email is associated with an existing
                  Mayavé account, your new order will be attached to it.)
                </Text>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default IdentificationStep;
