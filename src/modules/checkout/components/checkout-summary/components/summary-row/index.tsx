import Text from "@modules/ui-elements/text";

const SummaryRow = ({
  label,
  value,
  description,
  isBold,
}: {
  label: string;
  value: string;
  description?: string;
  isBold?: boolean;
}) => (
  <div className="flex items-start justify-between">
    <div>
      <Text as="p" size="body_large">
        {label}
      </Text>
      {description && (
        <Text
          as="p"
          size="body_medium"
          className="block max-w-[80%] text-theme-text-secondary"
        >
          {description}
        </Text>
      )}
    </div>
    <Text
      as="p"
      size={isBold ? "body_semi_bold" : "body_large"}
      className="whitespace-nowrap"
    >
      {value}
    </Text>
  </div>
);

export default SummaryRow;
