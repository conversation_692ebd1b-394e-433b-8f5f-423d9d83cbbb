import { HttpTypes } from "@medusajs/types";
import Divider from "@modules/common/divider";
import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import Text from "@modules/ui-elements/text";
import { convertToLocale } from "utils/helpers/common";

import SummaryRow from "./components/summary-row";

const CheckoutSummary = ({ cart }: { cart: HttpTypes.StoreCart }) => {
  const {
    currency_code,
    total,
    subtotal,
    tax_total,
    shipping_subtotal,
    discount_total,
  } = cart;
  return (
    <div className="h-fit w-full space-y-[30px] py-5 lg:space-y-[26px] lg:border lg:border-theme-other-black lg:p-[30px]">
      <div className="flex flex-col gap-y-5">
        <div className="flex items-center justify-between">
          <Text as="h6" size="body_semi_bold">
            MY SHOPPING CART
          </Text>
          <NextLink href="/cart" className="underline body-copy-2">
            Modify
          </NextLink>
        </div>
        <div className="flex flex-col gap-y-5">
          {cart.items?.map((item) => (
            <NextLink href={`/store/${item.product?.handle}`} key={item.id}>
              <div key={item.id} className="flex flex-col gap-y-5">
                <div className="flex gap-5">
                  <div className="relative h-[76px] w-[76px]">
                    <MediaDisplay src={item.thumbnail ?? ""} />
                  </div>
                  <div className="flex flex-col">
                    <Text
                      as="p"
                      size="body_large"
                      className="line-clamp-2 text-ellipsis"
                    >
                      {item.title}
                    </Text>
                    <Text as="p" size="body_large">
                      {convertToLocale({
                        amount: item.subtotal ?? 0,
                        currency_code,
                      })}
                    </Text>
                  </div>
                </div>
                <Divider className="mb-0 text-theme-text-primary" />
              </div>
            </NextLink>
          ))}
        </div>
      </div>
      <div className="flex flex-col">
        <SummaryRow
          label="Subtotal"
          value={convertToLocale({ amount: subtotal ?? 0, currency_code })}
        />
        <SummaryRow
          label="Shipping"
          value={convertToLocale({
            amount: shipping_subtotal ?? 0,
            currency_code,
          })}
        />
        <SummaryRow
          label="Tax"
          value={convertToLocale({ amount: tax_total ?? 0, currency_code })}
          description="(Will be calculated according to your delivery address)"
        />
        {discount_total ? (
          <SummaryRow
            label="Discount"
            value={convertToLocale({
              amount: discount_total ?? 0,
              currency_code,
            })}
          />
        ) : null}
      </div>
      <div className="mt-[45px] lg:mt-10">
        <SummaryRow
          label="Total"
          value={convertToLocale({ amount: total ?? 0, currency_code })}
          isBold
        />
      </div>
    </div>
  );
};

export default CheckoutSummary;
