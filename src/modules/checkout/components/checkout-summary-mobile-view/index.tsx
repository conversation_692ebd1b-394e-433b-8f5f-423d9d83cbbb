import React from "react";

import { HttpTypes } from "@medusajs/types";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@modules/common/accordion";
import Divider from "@modules/common/divider";
import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import Text from "@modules/ui-elements/text";
import { convertToLocale } from "utils/helpers/common";

import SummaryRow from "../checkout-summary/components/summary-row";

const CheckoutSummaryMobileView = ({ cart }: { cart: HttpTypes.StoreCart }) => {
  const {
    currency_code,
    total,
    subtotal,
    tax_total,
    shipping_subtotal,
    discount_total,
  } = cart;
  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-theme-other-white p-[30px]">
      <Accordion
        type="multiple"
        className="flex w-full flex-col gap-5 border-0"
      >
        <AccordionItem value="My Shopping Cart" className="border-0">
          <AccordionTrigger className="h-[26px] py-0">
            <Text as="h6" size="body_semi_bold">
              My Shopping Cart
            </Text>
          </AccordionTrigger>
          <AccordionContent className="border-0 pb-0">
            <div className="flex flex-col gap-y-5">
              <NextLink href="/cart" className="underline body-copy-2">
                Modify
              </NextLink>
              <div className="flex flex-col gap-y-5">
                {cart.items?.map((item) => (
                  <NextLink
                    href={`/store/${item.product?.handle}`}
                    key={item.id}
                  >
                    <div key={item.id} className="flex flex-col gap-y-5">
                      <div className="flex gap-5">
                        <div className="relative h-[76px] w-[76px]">
                          <MediaDisplay src={item.thumbnail ?? ""} />
                        </div>
                        <div className="flex flex-col">
                          <Text
                            as="p"
                            size="body_large"
                            className="line-clamp-2 text-ellipsis"
                          >
                            {item.title}
                          </Text>
                          <Text as="p" size="body_large">
                            {convertToLocale({
                              amount: item.subtotal ?? 0,
                            })}
                          </Text>
                        </div>
                      </div>
                      <Divider className="mb-0 text-theme-text-primary" />
                    </div>
                  </NextLink>
                ))}
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="AMOUNT SUMMARY">
          <AccordionTrigger className="h-[26px] py-0">
            <Text as="h6" size="body_semi_bold">
              AMOUNT SUMMARY
            </Text>
          </AccordionTrigger>
          <AccordionContent className="border-0 pb-0">
            <div className="flex flex-col">
              <SummaryRow
                label="Subtotal"
                value={convertToLocale({
                  amount: subtotal ?? 0,
                  currency_code,
                })}
              />
              <SummaryRow
                label="Shipping"
                value={convertToLocale({
                  amount: shipping_subtotal ?? 0,
                  currency_code,
                })}
              />
              <SummaryRow
                label="Tax"
                value={convertToLocale({
                  amount: tax_total ?? 0,
                  currency_code,
                })}
                description="(Will be calculated according to your delivery address)"
              />
              {discount_total ? (
                <SummaryRow
                  label="Discount"
                  value={convertToLocale({
                    amount: discount_total ?? 0,
                    currency_code,
                  })}
                />
              ) : null}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      <div className="mt-5 flex h-[26px] items-start justify-between">
        <div>
          <Text as="p" size="body_semi_bold">
            SUBTOTAL
          </Text>
        </div>
        <Text as="p" size="body_semi_bold" className="whitespace-nowrap">
          {convertToLocale({ amount: total ?? 0, currency_code })}
        </Text>
      </div>
    </div>
  );
};

export default CheckoutSummaryMobileView;
