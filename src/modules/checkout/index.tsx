import React from "react";

import { HttpTypes } from "@medusajs/types";
import CustomerSupportOptions from "@modules/cart/components/customer-support-options";
import NextLink from "@modules/common/next-link";
import Previous from "assets/icons/previous";

import AddressStep from "./components/address-step";
import CheckoutSummary from "./components/checkout-summary";
import CheckoutSummaryMobileView from "./components/checkout-summary-mobile-view";
import IdentificationStep from "./components/identification-step";

const Checkout = ({
  cart,
  customer,
}: {
  cart: HttpTypes.StoreCart;
  customer: HttpTypes.StoreCustomer | null;
}) => {
  console.log({ customer });
  return (
    <div className="py-10 content-container lg:py-[84px]">
      <div className="mb-5 flex items-center justify-between gap-4 md:mb-[46px]">
        <NextLink
          href="/cart"
          className="flex h-[21px] items-center"
          title="Back to Cart"
        >
          <Previous />
        </NextLink>
        <NextLink href="/jewellery" className="underline body-copy-2">
          Continue Shopping
        </NextLink>
      </div>
      <div className="flex w-full flex-col gap-10 lg:flex-row lg:justify-between lg:gap-[60px]">
        <div className="w-full space-y-16">
          <IdentificationStep cart={cart} />
          <AddressStep />
        </div>
        <div className="hidden w-full flex-col gap-10 md:flex lg:max-w-[443px] lg:gap-5">
          <CheckoutSummary cart={cart} />
          <CustomerSupportOptions />
        </div>
        <div className="block md:hidden">
          <CheckoutSummaryMobileView cart={cart} />
        </div>
      </div>
    </div>
  );
};

export default Checkout;
