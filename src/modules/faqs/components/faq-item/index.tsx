"use client";

import React, { useMemo, useState } from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@modules/common/dropdown";
import ArrowDown from "assets/icons/arrow-down";
import cn from "utils/helpers/cn";

import FAQAccordion from "./components/faq-accordion";
import { FaqData, FaqItem } from "./utils/types";

function FAQs({ block }: { block: FaqData }) {
  const faqsContent = block.faq_content;

  const faqTopics = faqsContent?.map((faq) => faq?.Topic) || [];

  const [selectedTopic, setSelectedTopic] = useState<string>(
    faqsContent?.[0]?.Topic
  );

  const getSelectedFAQContent = useMemo(() => {
    if (!selectedTopic) return null;
    return (
      faqsContent?.find((faq) => faq?.Topic === selectedTopic)?.QnA ||
      ([] as FaqItem[])
    );
  }, [selectedTopic, faqsContent]);

  return (
    <div className="flex w-full flex-col items-start justify-center md:flex-row">
      <div className="flex w-full flex-col items-center justify-end gap-5 md:w-1/3 md:p-10">
        <ul className="hidden flex-col items-start justify-center gap-5 md:flex">
          <li>
            <span className="body-copy">Choose a Topic:</span>
          </li>
          {faqTopics?.map((topic) => (
            <li key={topic}>
              <span
                className={cn(
                  "cursor-pointer body-copy-semi-bold",
                  selectedTopic === topic && "text-theme-text-secondary"
                )}
                onClick={() => setSelectedTopic(topic)}
              >
                {topic}
              </span>
            </li>
          ))}
        </ul>
        <div className="w-full md:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger className="btn flex h-[42px] w-full items-center justify-center gap-2 border-b border-b-theme-background-container body-copy">
              Choose a Topic <ArrowDown height={10} />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full bg-white" side="bottom">
              {faqTopics?.map((topic, index: number) => (
                <React.Fragment key={topic}>
                  <DropdownMenuItem
                    className={cn(
                      "flex w-screen cursor-pointer justify-center py-2 body-copy-semi-bold hover:outline-none",
                      selectedTopic === topic && "text-theme-text-secondary"
                    )}
                    onClick={() => setSelectedTopic(topic)}
                  >
                    {topic}
                  </DropdownMenuItem>
                  {index !== faqTopics?.length - 1 && (
                    <DropdownMenuSeparator className="bg-theme-background-container" />
                  )}
                </React.Fragment>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="relative flex w-full flex-col items-center justify-start gap-5 p-5 md:w-2/3 md:items-start md:p-10">
        <FAQAccordion content={getSelectedFAQContent || []} />
      </div>
    </div>
  );
}

export default FAQs;
