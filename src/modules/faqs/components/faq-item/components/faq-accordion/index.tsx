import React from "react";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@modules/common/accordion";

import { FaqItem } from "../../utils/types";

function FAQAccordion({ content }: { content: FaqItem[] }) {
  const allQuestionValues = React.useMemo(() => {
    return content?.map((faq) => faq?.Question) || [];
  }, [content]);

  return (
    <Accordion
      type="multiple"
      className="flex w-full flex-col gap-5 md:gap-10 lg:w-[80%]"
      defaultValue={allQuestionValues}
      key={allQuestionValues?.join(",")}
    >
      {content?.map((faq) => (
        <AccordionItem
          value={faq?.Question}
          key={faq?.id}
          className="pb-5 md:pb-10"
        >
          <AccordionTrigger>{faq?.Question}</AccordionTrigger>
          <AccordionContent>
            <span className="body-copy">{faq?.Answer}</span>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}

export default FAQAccordion;
