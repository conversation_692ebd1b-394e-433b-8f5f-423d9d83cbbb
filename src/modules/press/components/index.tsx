import { Suspense } from "react";

import { notFound } from "next/navigation";

import { DynamicPageProps } from "@modules/dynamic-page/utils/types";
import { getTemplateBlocks } from "utils/api/strapi-api";
import renderBlock from "utils/mappers/component-renderer";
import { getPressTemplateBlocksQuery } from "utils/strapi-api/press-template-query";

async function DynamicPressPage(Props: DynamicPageProps) {
  const { page, page_type } = Props;

  const blocks = await getTemplateBlocks(
    Props.template_name,
    getPressTemplateBlocksQuery
  );

  if (blocks.length === 0) notFound();

  const rendered_blocks = blocks.map((block, index) =>
    renderBlock(block, index, page, page_type)
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicPressPage;
