import React from "react";

import RichtextBlockRenderer from "@modules/common/richtext-block-renderer";

import { PressHeroBlock } from "./utils/types";

function PressHero({ block }: PressHeroBlock) {
  return (
    <div
      className={`flex h-[280px] w-full flex-col items-center justify-center`}
    >
      <p className={`max-w-[619px] break-words text-center h1`}>
        {block?.heading}
      </p>

      {block.subheading && (
        <div className={`mt-3 break-words text-center body-copy`}>
          <RichtextBlockRenderer content={block.subheading} />
        </div>
      )}
    </div>
  );
}

export default PressHero;
