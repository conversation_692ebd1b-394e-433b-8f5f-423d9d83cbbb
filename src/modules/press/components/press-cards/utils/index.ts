interface FormatDateOptions {
  day?: boolean;
  month?: boolean;
  year?: boolean;
  dayName?: boolean;
  monthName?: boolean;
  shortDay?: boolean;
  longDay?: boolean;
  shortMonth?: boolean;
  longMonth?: boolean;
  shortYear?: boolean;
  longYear?: boolean;
  separator?: string;
  fullDate?: boolean;
}

export function formatDate(
  dateInput: string | Date,
  options: FormatDateOptions = {}
): string | null {
  const date = typeof dateInput === "string" ? new Date(dateInput) : dateInput;
  if (isNaN(date.getTime())) return ""; // Invalid date

  const {
    day = false,
    month = false,
    year = false,
    dayName = false,
    monthName = false,
    shortDay = false,
    longDay = false,
    shortMonth = false,
    longMonth = false,
    shortYear = false,
    longYear = false,
    separator = " ",
    fullDate = false,
  } = options;

  // Helper functions to get localized names
  const getDayName = (length: "short" | "long") =>
    date.toLocaleDateString("en-US", { weekday: length });

  const getMonthName = (length: "short" | "long") =>
    date.toLocaleDateString("en-US", { month: length });

  const parts: string[] = [];

  if (fullDate) {
    // Return ISO-like yyyy-mm-dd format
    parts.push(date.toLocaleDateString("en-CA"));
  } else {
    if (dayName) {
      parts.push(getDayName("long"));
    } else if (shortDay) {
      parts.push(getDayName("short"));
    } else if (longDay) {
      parts.push(getDayName("long"));
    }

    if (day) {
      parts.push(String(date.getDate()).padStart(2, "0"));
    }

    if (monthName) {
      parts.push(getMonthName("long"));
    } else if (shortMonth) {
      parts.push(getMonthName("short"));
    } else if (longMonth) {
      parts.push(getMonthName("long"));
    }

    if (month) {
      const m = date.getMonth() + 1;
      parts.push(String(m).padStart(2, "0"));
    }

    if (shortYear) {
      parts.push(String(date.getFullYear()).slice(2));
    } else if (year || longYear) {
      parts.push(String(date.getFullYear()));
    }
  }

  return parts.join(separator);
}
