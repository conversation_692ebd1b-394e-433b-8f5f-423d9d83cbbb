import { Media } from "utils/types/common";

export interface PressArticleCard {
  id: number;
  tag: string;
  publish_date: string;
  thumbnail: {
    id: string;
    title: string;
    subtitle: string;
    cta_title: string;
    cta_link: string;
    desktop_media: Media;
    mobile_media: Media;
    description: string;
    responsive: string;
  };
}

export interface PressArticleBlock {
  article_cards: PressArticleCard[];
  id: string;
  __typename: string;
}
