import React from "react";

import MediaDisplay from "@modules/common/media-display";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { formatDate } from "./utils";
import { PressArticleBlock, PressArticleCard } from "./utils/types";

function PressArticleCards({ block }: { block: PressArticleBlock }) {
  return (
    <div className="mb-16 grid w-full grid-cols-1 items-start justify-center gap-x-5 gap-y-8 px-5 md:grid-cols-2 md:gap-y-10 lg:grid-cols-3 lg:gap-y-14">
      {block?.article_cards?.map((card, index) => {
        return <PressArticle key={`card-${index}`} {...card} />;
      })}
    </div>
  );
}

export default PressArticleCards;

const PressArticle = (props: PressArticleCard) => {
  const { thumbnail, tag, publish_date } = props;

  return (
    <div className="group flex w-full cursor-pointer items-center justify-center">
      <div className="flex w-[420px] flex-col items-start justify-start gap-y-2">
        <div className="flex w-full items-center justify-start text-center">
          <Text as="p" size="body_medium" className="text-theme-text-secondary">
            {tag}, {formatDate(publish_date, { month: true })}.
            {formatDate(publish_date, { year: true })}
          </Text>
        </div>
        <MediaDisplay
          src={thumbnail?.desktop_media?.url}
          className="h-[226px] w-full object-cover transition-all duration-150 group-hover:scale-[1.02]"
        />
        <div className="flex w-full flex-col items-start justify-start gap-4 text-center">
          <Text as="p" size="body_semi_bold" className="h-10 w-full text-left">
            {thumbnail?.title}
          </Text>
          <Button className="w-fit border-theme-text-secondary text-theme-text-secondary">
            {thumbnail?.cta_title}
          </Button>
        </div>
      </div>
    </div>
  );
};
