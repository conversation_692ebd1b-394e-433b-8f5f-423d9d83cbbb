import { notFound } from "next/navigation";

import { getPageTemplateName } from "utils/api/strapi-api";

import DynamicProductDetails from "./components";

async function ProductDetailsSection({ handle }: { handle: string }) {
  const page = `jewellery/${handle}`;

  const [{ template_name, page_type }] = await Promise.all([
    getPageTemplateName(page),
  ]);

  if (!template_name || !page_type) {
    notFound();
  }

  return (
    <DynamicProductDetails
      page={page}
      page_type={page_type}
      template_name={template_name}
    />
  );
}

export default ProductDetailsSection;
