"use client";

import { useState } from "react";

import { redirect, useSearchParams } from "next/navigation";

import AccordionSmooth from "@modules/common/accordion-smooth";
import Divider from "@modules/common/divider";
import LikeButton from "@modules/common/like-button";
import Button from "@modules/ui-elements/button";
import Input from "@modules/ui-elements/input";
import Text from "@modules/ui-elements/text";
import { addToCart } from "utils/api/server-api/cart";
import cn from "utils/helpers/cn";

import { ProductDetails } from "../../utils/types";

interface HeroDetailsAccordionProps extends ProductDetails {
  defaultOpen?: boolean;
}

function HeroDetailsAccordion({
  product_title,
  product_price,
  product_description,
  defaultOpen,
}: HeroDetailsAccordionProps) {
  const [isLiked, setIsLiked] = useState(false);

  const params = useSearchParams();

  const [isAdding, setIsAdding] = useState(false);

  const variantId = params.get("vid");

  const handleAccordionOpen = (status: boolean) => {
    if (!status) return;

    const currentScrollY = window.scrollY;

    if (currentScrollY > 50) {
      window.scrollTo({ top: 100, behavior: "smooth" });
    }
  };

  const handleAddToCart = async () => {
    if (!variantId) return null;

    setIsAdding(true);

    await addToCart({
      variantId: variantId,
      quantity: 1,
    });

    setIsAdding(false);
    redirect("/cart");
  };

  const HeaderContent = (
    <div className="flex w-full flex-col items-start justify-between gap-3 lg:flex-row lg:items-center">
      <Text as="h2" size="body_semi_bold">
        {product_title}
      </Text>
      <div className="flex items-center justify-center gap-2">
        <Text as="h2" size="body_semi_bold">
          {product_price}
        </Text>
      </div>
    </div>
  );

  const BodyContent = (
    <div className="mt-4">
      <Text as="p" size="body_large">
        {product_description}
      </Text>
      <Divider className="bg-theme-text-secondary" />
      <div className="flex w-full flex-col">
        <Text size="body_medium">Metals</Text>
      </div>
      <Divider className="bg-theme-text-secondary" />
      <div className="flex w-full items-center justify-between">
        <Text size="body_medium">Select Sizes</Text>
        <Text size="body_medium" className="cursor-pointer">
          Size Guide
        </Text>
      </div>
      <Divider className="bg-theme-text-secondary" />
      <div className="grid w-full grid-cols-5 items-center justify-between gap-3 md:gap-5">
        <Button
          className="col-span-5 grid h-10 md:col-span-2"
          disabled={isAdding}
          loading={isAdding}
          onClick={handleAddToCart}
        >
          Add To Cart
        </Button>
        <Button className="col-span-4 h-10 md:col-span-2">
          Make it a Gift
        </Button>
        <Button
          className="col-span-1 h-10 w-fit md:col-span-1"
          onClick={() => {
            setIsLiked(!isLiked);
          }}
        >
          <LikeButton isLiked={isLiked} setIsLiked={setIsLiked} />
        </Button>
      </div>
      <Divider className="bg-theme-text-secondary" />
      <div className="flex w-full flex-col gap-2">
        <Text size="body_medium">Enter Pincode</Text>
        <div className="flex items-start">
          <div className="w-[140px] border border-theme-background-container pl-2">
            <Input
              type="number"
              className="bg-transparent"
              wrapperClassName={"border-0 h-[24px]  "}
              maxLength={6}
            />
          </div>
          <Button className="h-[26px] w-[131px] text-nowrap border-0 bg-theme-text-secondary text-theme-text-contrast">
            Submit
          </Button>
        </div>
        <div className="flex items-center justify-start gap-2 pt-2">
          <div className="h-2 w-2 rounded-full bg-green-600" />
          <Text size="body_medium">Express Delivery Available</Text>
        </div>
        <div className="flex items-center justify-start gap-2">
          <div className="h-2 w-2 rounded-full bg-theme-text-secondary" />
          <Text size="body_medium">
            Estimated Delivery on Thursday, May 5, 2025
          </Text>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <div className="hidden lg:block">
        <AccordionSmooth
          className="w-[484px]"
          accordionHeader={HeaderContent}
          defaultOpen={defaultOpen}
          accordionBody={BodyContent}
          handleAccordionState={handleAccordionOpen}
        />
      </div>
      <div className="block lg:hidden">
        <div className={cn("flex w-full flex-col items-start justify-start")}>
          {HeaderContent}
          <div className="overflow-hidden duration-300 ease-in-out">
            {BodyContent}
          </div>
        </div>
      </div>
    </>
  );
}

export default HeroDetailsAccordion;
