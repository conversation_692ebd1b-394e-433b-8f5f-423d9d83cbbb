"use client";

import React from "react";

import { Carousel, CarouselContent } from "@modules/common/carousel";
import MediaDisplay from "@modules/common/media-display";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Text from "@modules/ui-elements/text";
import AutoScroll from "embla-carousel-auto-scroll";

import HeroDetailsAccordion from "./components/detail-accordion";
import { ProductDetails } from "./utils/types";

function ProductDetailsHero({
  block,
}: DynamicPageComponentProps<ProductDetails>) {
  const hero_medias = block.hero;

  return (
    <div className="block">
      <Text className="p-5" as="h1" size="headline">
        {block.product_title}
      </Text>
      <Carousel
        className="relative"
        opts={{ loop: true }}
        plugins={[AutoScroll()]}
        autoplay
      >
        <CarouselContent
          onClick={(e) => {
            e.stopPropagation();
            console.log("clicked");
          }}
        >
          {hero_medias.map((item, index: number) => {
            return (
              <React.Fragment key={index}>
                <MediaDisplay
                  src={item.desktop_media.url}
                  className="h-[721px] w-[749px]"
                />
              </React.Fragment>
            );
          })}
        </CarouselContent>
        <div className="absolute bottom-20 right-16 hidden bg-white bg-opacity-95 p-5 lg:flex">
          <HeroDetailsAccordion {...block} />
        </div>
      </Carousel>
      <div className="flex bg-white p-5 lg:hidden">
        <HeroDetailsAccordion {...block} defaultOpen={true} />
      </div>
    </div>
  );
}

export default ProductDetailsHero;
