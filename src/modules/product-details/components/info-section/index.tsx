"use client";

import React from "react";

import AccordionSmooth from "@modules/common/accordion-smooth";
import Divider from "@modules/common/divider";
import MediaDisplay from "@modules/common/media-display";
import KeyBenefitsBarSmall from "@modules/home/<USER>/key-benefits-bar-small";
import {
  BenefitBarContent,
  ProductInfoAccordionData,
} from "@modules/product-details/constants";
import Text from "@modules/ui-elements/text";
import { PageTypeEnum } from "utils/types/common";

import { ProductDetails } from "../hero/utils/types";

function ProductInfoSection({ block }: { block: ProductDetails }) {
  const product_image = block.product_image[0];

  const [openIndex, setOpenIndex] = React.useState(-1);

  return (
    <div className="flex w-full flex-col items-center justify-center gap-10 px-5 py-5 lg:flex-row lg:items-start lg:px-0 lg:py-20">
      <MediaDisplay
        src={product_image.desktop_media.url}
        className="h-[564px] w-full bg-theme-background-container lg:w-[459px]"
      />
      <div className="flex w-full flex-col gap-2 bg-theme-background-container p-4 lg:w-[382px] lg:gap-10 lg:p-8">
        {ProductInfoAccordionData?.accordionItems?.map(
          (item, index: number) => {
            return (
              <React.Fragment key={`accordion-${index}`}>
                <AccordionSmooth
                  handleAccordionState={(isOpen) => {
                    if (isOpen) {
                      setOpenIndex(index);
                    } else {
                      setOpenIndex(-1);
                    }
                  }}
                  defaultOpen={openIndex === index}
                  accordionHeader={<Text>{item.title}</Text>}
                  accordionHeaderClassName="justify-between lg:justify-start gap-4"
                  accordionBody={
                    <div className="flex w-full flex-col items-start justify-between gap-5 pt-4">
                      {typeof item.content === "string" ? (
                        <Text size="body_large" as="p">
                          {item.content}
                        </Text>
                      ) : (
                        <>
                          {Object.keys(item.content).map((key) => {
                            return (
                              <div
                                key={key}
                                className="flex w-full items-center justify-between gap-10"
                              >
                                <Text
                                  size="body_medium"
                                  className="w-[100px] uppercase"
                                >
                                  {key}
                                </Text>
                                <Text
                                  size="body_medium"
                                  className="w-10 flex-1 break-words"
                                >
                                  {item.content[key]}
                                </Text>
                              </div>
                            );
                          })}
                        </>
                      )}
                    </div>
                  }
                />
                <Divider className="block h-[1px] bg-theme-text-secondary lg:hidden" />
              </React.Fragment>
            );
          }
        )}
        <KeyBenefitsBarSmall
          block={{
            id: "",
            __typename: "",
            benefits_bar: BenefitBarContent,
          }}
          isFirst={false}
          page=""
          page_type={"HOME" as PageTypeEnum}
        />
      </div>
    </div>
  );
}

export default ProductInfoSection;
