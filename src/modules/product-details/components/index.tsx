import { Suspense } from "react";

import { notFound } from "next/navigation";

import { DynamicPageProps } from "@modules/dynamic-page/utils/types";
import { getTemplateBlocks } from "utils/api/strapi-api";
import renderBlock from "utils/mappers/component-renderer";
import { getPDPTemplateBlocksQuery } from "utils/strapi-api/pdp-template-query";

import ProductInfoSection from "./info-section";

async function DynamicProductDetails({
  page,
  page_type,
  template_name,
}: DynamicPageProps) {
  const blocks = await getTemplateBlocks(
    template_name,
    getPDPTemplateBlocksQuery
  );

  if (blocks.length === 0) notFound();

  const rendered_blocks = blocks.map((block, index) =>
    renderBlock(block, index, page, page_type)
  );

  const heroBlock = blocks.find(
    (item) => item.__typename === "ComponentPdpProductDetails"
  );

  rendered_blocks.splice(
    1,
    0,
    <ProductInfoSection block={heroBlock} key="static-block" />
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicProductDetails;
