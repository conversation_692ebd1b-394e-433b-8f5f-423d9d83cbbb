import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
} from "@modules/common/carousel";

import RelatedProductCard from "./components/related-product-card";
import { RelatedProductSectionType } from "./utils/types";

function RelatedProductSections({
  block,
}: {
  block: RelatedProductSectionType;
}) {
  const relatedProducts = block?.related_products;

  const heading = block?.heading;

  return (
    <div className="flex w-full flex-col items-center justify-center py-10">
      <p className="w-1/2 pb-14 pt-14 text-center h1 lg:w-full lg:pt-32">
        {heading}
      </p>
      <div className="flex w-full items-start justify-center gap-10 px-5 lg:px-20">
        <Carousel
          autoplay
          autoplayDuration={5000}
          opts={{ loop: true }}
          className="relative w-full"
        >
          <CarouselContent>
            {relatedProducts.map((product, index) => (
              <CarouselItem key={index} className="basis-full md:basis-1/3">
                <RelatedProductCard {...product} />
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="block pt-5 lg:hidden">
            <CarouselDots slidesLength={relatedProducts.length} />
          </div>
        </Carousel>
      </div>
    </div>
  );
}

export default RelatedProductSections;
