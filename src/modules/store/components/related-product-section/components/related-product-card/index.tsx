"use client";

import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselNext,
  CarouselPrevious,
} from "@modules/common/carousel";
import LikeButton from "@modules/common/like-button";
import MediaDisplay from "@modules/common/media-display";
import Button from "@modules/ui-elements/button";
import Fade from "embla-carousel-fade";

import { RelatedProduct } from "../../utils/types";

function RelatedProductCard(props: RelatedProduct) {
  const { Heading, description, price, product_image } = props;

  return (
    <div className="group relative flex w-full flex-col items-center justify-start gap-2">
      <div className="absolute right-5 top-5 z-50 cursor-pointer opacity-0 transition-all duration-200 group-hover:opacity-100">
        <LikeButton />
      </div>
      <Carousel className="relative" opts={{ loop: true }} plugins={[Fade()]}>
        <CarouselContent>
          <MediaDisplay src={product_image.url} />
          <MediaDisplay src={product_image.url} />
        </CarouselContent>
        <CarouselDots
          slidesLength={2}
          className="absolute bottom-5 left-1/2 block h-auto translate-x-[-50%] pt-5 opacity-0 transition-all duration-200 group-hover:opacity-100"
        />
        <CarouselPrevious className="absolute left-4 top-1/2 h-10 w-10 -translate-y-1/2 transform border-0 opacity-0 transition-all duration-200 hover:bg-transparent hover:text-black group-hover:opacity-100" />
        <CarouselNext className="absolute right-4 top-1/2 h-10 w-10 -translate-y-1/2 transform border-0 opacity-0 transition-all duration-200 hover:bg-transparent hover:text-black group-hover:opacity-100" />{" "}
      </Carousel>
      <p className="text-center body-copy-semi-bold lg:h-auto">{Heading}</p>
      <p className="w-2/3 text-center text-xs body-copy lg:text-sm">
        {description}
      </p>
      <p className="body-copy-semi-bold">{price}</p>
      <Button className="w-fit opacity-0 transition-all duration-200 group-hover:opacity-100">
        Add To Cart
      </Button>
    </div>
  );
}

export default RelatedProductCard;
