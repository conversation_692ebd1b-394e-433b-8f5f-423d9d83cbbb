import { Media, ResponsiveType } from "utils/types/common";

export interface ProductListingBannerType {
  id: string;
  __typename: "ComponentStoreProductListingBanner";
  store_banner: StoreBanner;
}

export interface StoreBanner {
  id: string;
  module: string;
  section: string;
  media: StoreMedia[];
  index: string;
}

export interface StoreMedia {
  id: string;
  title: string;
  subtitle: string;
  cta_title: string;
  cta_link: string;
  desktop_media: Media;
  mobile_media: Media;
  description: string;
  responsive: ResponsiveType;
  theme: "light" | "dark";
}
