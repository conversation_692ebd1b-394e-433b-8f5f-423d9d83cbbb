import React from "react";

import MediaDisplay from "@modules/common/media-display";

import { ProductListingBannerType } from "./utils/types";

function ProductListingBanner({ block }: { block: ProductListingBannerType }) {
  const bannerMediaData = block?.store_banner?.media?.[0];

  const bgImage = bannerMediaData.desktop_media;

  return (
    <div className="relative w-full">
      <MediaDisplay
        src={bgImage.url}
        className="h-[450px] w-full object-cover object-[70%_center] lg:h-[481px] lg:object-center"
      />

      <div className="absolute inset-0 left-0 flex h-full flex-col items-start justify-end gap-5 pb-10 lg:left-[13%] lg:justify-center lg:gap-2 lg:pb-0">
        <p className="w-full text-center text-theme-text-contrast h1 lg:text-left">
          {bannerMediaData?.title}
        </p>
        {bannerMediaData?.subtitle && (
          <p className="w-full text-center text-theme-text-contrast body-copy lg:text-left">
            {bannerMediaData?.subtitle}
          </p>
        )}
        {bannerMediaData?.description && (
          <p className="flex w-full justify-center text-center text-theme-text-contrast body-copy lg:w-[30%] lg:text-left">
            <span className="w-[90%] lg:w-full">
              {bannerMediaData?.description}
            </span>
          </p>
        )}
      </div>
    </div>
  );
}

export default ProductListingBanner;
