import { Suspense } from "react";

import { notFound } from "next/navigation";

import { DynamicPageProps } from "@modules/dynamic-page/utils/types";
import { getTemplateBlocks } from "utils/api/strapi-api";
import renderBlock from "utils/mappers/component-renderer";
import { getStoreTemplateBlocksQuery } from "utils/strapi-api/store-template-query";

import ProductListing from "./product-listing";
import { StorePageParams } from "../utils/types";

async function StoreDynamicPage(
  Props: DynamicPageProps & { queryParams: StorePageParams["searchParams"] }
) {
  const { page, page_type } = Props;

  const blocks = await getTemplateBlocks(
    Props.template_name,
    getStoreTemplateBlocksQuery
  );

  if (blocks.length === 0) notFound();

  const rendered_blocks = blocks.map((block, index) =>
    renderBlock(block, index, page, page_type)
  );

  const promotionalBlock = blocks.find(
    (item) => item.__typename === "ComponentStorePromotionalBanner"
  );

  rendered_blocks.splice(
    1,
    0,
    <ProductListing
      key="static-block"
      block={promotionalBlock}
      queryParams={Props.queryParams}
    />
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default StoreDynamicPage;
