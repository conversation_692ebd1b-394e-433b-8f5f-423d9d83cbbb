import { Media, ResponsiveType } from "utils/types/common";

export interface PromotionalBannerResponse {
  id: string;
  __typename: "ComponentStorePromotionalBanner";
  promotional_banner: ComponentCommonBanner[];
}

export interface ComponentCommonBanner {
  id: string;
  __typename: "ComponentCommonBanner";
  module: string;
  section: string;
  index: string;
  media: BannerMedia[];
}

export interface BannerMedia {
  id: string;
  title: string;
  subtitle: string;
  cta_title: string;
  cta_link: string;
  desktop_media: Media;
  mobile_media: Media;
  description: string;
  responsive: ResponsiveType;
}
