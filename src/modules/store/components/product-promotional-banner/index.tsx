import React from "react";

import MediaDisplay from "@modules/common/media-display";
import Button from "@modules/ui-elements/button";

import { PromotionalBannerResponse } from "./utils/types";

function ProductPromotionalBanner({
  block,
}: {
  block: PromotionalBannerResponse;
}) {
  const promotionalBannerData = block.promotional_banner[0].media[0];

  return (
    <div className="flex flex-col items-center justify-start gap-6">
      <MediaDisplay
        src={promotionalBannerData.desktop_media.url}
        className="object-cover object-[70%_center]"
      />
      <p className="text-center body-copy">
        {promotionalBannerData.description}
      </p>
      <Button className="w-fit">{promotionalBannerData.cta_title}</Button>
    </div>
  );
}

export default ProductPromotionalBanner;
