import React from "react";

import { StorePageParams } from "@modules/store/utils/types";
import { listProducts } from "utils/api/server-api/products";

import ProductPromotionalBanner from "../product-promotional-banner";
import ProductCard from "./components/product-card";
import { PromotionalBannerResponse } from "../product-promotional-banner/utils/types";

const ProductListing = async ({
  block,
  queryParams,
}: {
  block: PromotionalBannerResponse;
  queryParams?: StorePageParams["searchParams"];
}) => {
  const promotionalBanners = block.promotional_banner;

  const bannerMap = new Map<number, (typeof promotionalBanners)[number]>();
  promotionalBanners.forEach((banner) => {
    const index = Number(banner.index);
    if (!isNaN(index)) {
      bannerMap.set(index, banner);
    }
  });

  let {
    response: { products },
  } = await listProducts({
    pageParam: queryParams?.offset || 1,
    queryParams,
  });

  return (
    <>
      {/* <Filters /> */}
      <div className="grid w-full grid-cols-2 items-start justify-center gap-x-2 gap-y-5 p-5 sm:p-10 md:grid-cols-4 md:gap-5 lg:gap-10 lg:p-20 lg:pb-0">
        {products.map((product, index) => {
          const banner = bannerMap.get(index + 1); // index is 0-based, banner index is 1-based

          return (
            <React.Fragment key={index}>
              {banner && (
                <div className="col-span-2 row-span-2 md:col-span-2 md:row-span-2">
                  <ProductPromotionalBanner
                    block={{
                      promotional_banner: [banner],
                      __typename: "ComponentStorePromotionalBanner",
                      id: banner.id,
                    }}
                  />
                </div>
              )}
              <ProductCard product={product} />
            </React.Fragment>
          );
        })}
      </div>
    </>
  );
};

export default ProductListing;
