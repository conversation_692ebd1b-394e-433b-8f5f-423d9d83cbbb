import React from "react";

import { notFound } from "next/navigation";

import { getPageTemplateName } from "utils/api/strapi-api";
import { PageTypeEnum } from "utils/types/common";

import StoreDynamicPage from "./components";
import { StorePageParams } from "./utils/types";

async function Store({ searchParams }: StorePageParams) {
  const page = "jewellery";

  const [{ template_name, page_type }] = await Promise.all([
    getPageTemplateName(page),
  ]);

  if (!template_name || !page_type) {
    console.warn(`-> No template name found for page: ${page}`);
    notFound();
  }
  return (
    <StoreDynamicPage
      page={page}
      page_type={PageTypeEnum.Home}
      template_name={template_name}
      queryParams={searchParams}
    />
  );
}

export default Store;
