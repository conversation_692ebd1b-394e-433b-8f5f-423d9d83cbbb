import { HttpTypes } from "@medusajs/types";

// check if the selected variant is in stock
export const checkProductInStock = (
  defaultVariant: HttpTypes.StoreProductVariant
) => {
  // If we don't manage inventory, we can always add to cart
  if (defaultVariant && !defaultVariant.manage_inventory) {
    return true;
  }

  // If we allow back orders on the variant, we can add to cart
  if (defaultVariant?.allow_backorder) {
    return true;
  }

  // If there is inventory available, we can add to cart
  if (
    defaultVariant?.manage_inventory &&
    (defaultVariant?.inventory_quantity || 0) > 0
  ) {
    return true;
  }

  // Otherwise, we can't add to cart
  return false;
};
