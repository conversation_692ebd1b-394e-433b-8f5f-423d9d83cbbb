import React from "react";

import { CenterBannerBlock } from "./utils/types";

function CenterBanner({ block, height }: CenterBannerBlock) {
  const theme = block.theme ?? "DARK";

  const h = height ?? "440px";

  return (
    <div
      style={{
        height: h,
      }}
      className={`flex w-full flex-col items-center justify-center px-5 py-20 md:px-0 md:py-0 ${theme === "DARK" ? "bg-theme-background-accent" : ""} `}
    >
      <p
        className={`max-w-[619px] break-words text-center ${theme === "DARK" ? "text-theme-text-contrast" : ""} h1`}
      >
        {block?.heading}
      </p>
      {block?.richtext_description && (
        <p
          className={`mt-3 break-words text-center ${theme === "DARK" ? "text-theme-text-contrast" : ""} body-copy`}
          dangerouslySetInnerHTML={{
            __html: block?.richtext_description?.replace(/\n/g, "<br />"),
          }}
        ></p>
      )}
      {block.description && (
        <p
          className={`mt-3 break-words text-center ${theme === "DARK" ? "text-theme-text-contrast" : ""} body-copy`}
        >
          {block?.description}
        </p>
      )}
    </div>
  );
}

export default CenterBanner;
