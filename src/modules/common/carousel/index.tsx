"use client";

import * as React from "react";

import Button from "@modules/ui-elements/button";
import ArrowLeft from "assets/icons/arrow-left";
import ArrowRight from "assets/icons/arrow-right";
import useEmblaCarousel, {
  type UseEmblaCarouselType,
} from "embla-carousel-react";
import { cn } from "utils/helpers/cn";

// Types
export type CarouselApi = UseEmblaCarouselType[1];
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>;
type CarouselOptions = UseCarouselParameters[0];
type CarouselPlugin = UseCarouselParameters[1];

export type CarouselProps = {
  opts?: CarouselOptions;
  plugins?: CarouselPlugin;
  orientation?: "horizontal" | "vertical";
  setApi?: (api: CarouselApi) => void;
  autoplay?: boolean;
  autoplayDuration?: number;
};

type CarouselContextProps = {
  carouselRef: ReturnType<typeof useEmblaCarousel>[0];
  api: ReturnType<typeof useEmblaCarousel>[1];
  scrollPrev: () => void;
  scrollNext: () => void;
  canScrollPrev: boolean;
  canScrollNext: boolean;
  currentIndex: number;
  scrollProgress: number;
} & CarouselProps;

const CarouselContext = React.createContext<CarouselContextProps | null>(null);

function useCarousel() {
  const context = React.useContext(CarouselContext);
  if (!context) {
    throw new Error("useCarousel must be used within a <Carousel />");
  }
  return context;
}

const Carousel = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & CarouselProps
>(
  (
    {
      orientation = "horizontal",
      opts,
      setApi,
      plugins,
      className,
      children,
      autoplay,
      autoplayDuration = 3000,
      ...props
    },
    ref
  ) => {
    const [carouselRef, api] = useEmblaCarousel(
      {
        ...opts,
        axis: orientation === "horizontal" ? "x" : "y",
      },
      plugins
    );

    const [canScrollPrev, setCanScrollPrev] = React.useState(false);
    const [canScrollNext, setCanScrollNext] = React.useState(false);
    const [currentIndex, setCurrentIndex] = React.useState(0);
    const [scrollProgress, setScrollProgress] = React.useState(0);
    const [isPaused, setIsPaused] = React.useState(false);

    const onSelect = React.useCallback((api: CarouselApi) => {
      if (!api) return;
      setCurrentIndex(api.selectedScrollSnap());
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    }, []);

    const onScroll = React.useCallback((api: CarouselApi) => {
      if (!api) return;
      setScrollProgress(Math.min(Math.max(api.scrollProgress() * 100, 0), 100));
    }, []);

    const scrollPrev = React.useCallback(() => api?.scrollPrev(), [api]);
    const scrollNext = React.useCallback(() => api?.scrollNext(), [api]);

    const handleKeyDown = React.useCallback(
      (event: React.KeyboardEvent<HTMLDivElement>) => {
        if (event.key === "ArrowLeft") {
          event.preventDefault();
          scrollPrev();
        } else if (event.key === "ArrowRight") {
          event.preventDefault();
          scrollNext();
        }
      },
      [scrollPrev, scrollNext]
    );

    React.useEffect(() => {
      if (!api) return;
      onSelect(api);
      onScroll(api);
      api.on("select", onSelect);
      api.on("scroll", onScroll);
      api.on("reInit", () => {
        onSelect(api);
        onScroll(api);
      });
      return () => {
        api?.off("select", onSelect);
        api?.off("scroll", onScroll);
      };
    }, [api, onSelect, onScroll]);

    React.useEffect(() => {
      if (!api || !autoplay || isPaused) return;
      const interval = setInterval(() => {
        if (api.canScrollNext()) {
          api.scrollNext();
        } else {
          api.scrollTo(0);
        }
      }, autoplayDuration);
      return () => clearInterval(interval);
    }, [api, autoplay, autoplayDuration, isPaused]);

    React.useEffect(() => {
      if (!api || !setApi) return;
      setApi(api);
    }, [api, setApi]);

    return (
      <CarouselContext.Provider
        value={{
          carouselRef,
          api,
          opts,
          orientation:
            orientation || (opts?.axis === "y" ? "vertical" : "horizontal"),
          scrollPrev,
          scrollNext,
          canScrollPrev,
          canScrollNext,
          currentIndex,
          scrollProgress,
          autoplay,
          autoplayDuration,
        }}
      >
        <div
          ref={ref}
          onKeyDownCapture={handleKeyDown}
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
          className={cn("relative", className)}
          role="region"
          aria-roledescription="carousel"
          {...props}
        >
          {children}
        </div>
      </CarouselContext.Provider>
    );
  }
);

Carousel.displayName = "Carousel";

const CarouselContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { carouselRef, orientation } = useCarousel();
  return (
    <div ref={carouselRef} className="h-full overflow-hidden">
      <div
        ref={ref}
        className={cn(
          "flex",
          orientation === "horizontal" ? "-ml-4" : "-mt-4 flex-col",
          className
        )}
        {...props}
      />
    </div>
  );
});
CarouselContent.displayName = "CarouselContent";

const CarouselItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { orientation } = useCarousel();
  return (
    <div
      ref={ref}
      role="group"
      aria-roledescription="slide"
      className={cn(
        "min-w-0 shrink-0 grow-0 basis-full select-none pl-4",
        // orientation === "horizontal" ? "pl-4" : "pt-4",
        orientation === "horizontal" && "",
        className
      )}
      {...props}
    />
  );
});
CarouselItem.displayName = "CarouselItem";

const CarouselDots = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    slidesLength: number;
    dotsWrapperClassName?: string;
  }
>(({ className, slidesLength, dotsWrapperClassName, ...props }, ref) => {
  const { currentIndex, api } = useCarousel();

  return (
    <div
      ref={ref}
      className={cn("h-full overflow-hidden", className)}
      {...props}
    >
      {slidesLength > 1 && (
        <div className={cn(dotsWrapperClassName, "flex justify-center gap-2")}>
          {Array.from({ length: slidesLength }).map((_, index) => (
            <button
              key={index}
              onClick={() => api?.scrollTo(index)}
              className={cn(
                "h-1.5 w-1.5 rounded-full transition-all duration-300",
                index === currentIndex ? "w-5 bg-black" : "bg-gray-300"
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
});
CarouselDots.displayName = "CarouselDots";

const CarouselPrevious = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button>
>(({ className, ...props }, ref) => {
  const { orientation, scrollPrev, canScrollPrev } = useCarousel();
  return (
    <Button
      ref={ref}
      className={cn(
        "h-12 w-12 rounded-full",
        orientation === "horizontal" && "",
        // ? "-left-12 top-1/2 -translate-y-1/2"
        // : "-top-12 left-1/2 -translate-x-1/2 rotate-90",
        className
      )}
      disabled={!canScrollPrev}
      onClick={(e) => {
        e.stopPropagation();
        scrollPrev();
      }}
      {...props}
    >
      <ArrowLeft />
      <span className="sr-only">Previous slide</span>
    </Button>
  );
});
CarouselPrevious.displayName = "CarouselPrevious";

const CarouselNext = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button>
>(({ className, ...props }, ref) => {
  const { orientation, scrollNext, canScrollNext } = useCarousel();
  return (
    <Button
      ref={ref}
      className={cn(
        "h-12 w-12 rounded-full",
        orientation === "horizontal" && "",
        // ? "-right-12 top-1/2 -translate-y-1/2"
        // : "-bottom-12 left-1/2 -translate-x-1/2 rotate-90",
        className
      )}
      disabled={!canScrollNext}
      onClick={(e) => {
        e.stopPropagation();
        scrollNext();
      }}
      {...props}
    >
      <ArrowRight />
      <span className="sr-only">Next slide</span>
    </Button>
  );
});
CarouselNext.displayName = "CarouselNext";

export const CustomCarouselNavigation = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button> & {
    buttonType: "next" | "prev";
  }
>(({ className, buttonType = "next", children, ...props }, ref) => {
  const { scrollNext, canScrollNext, scrollPrev, canScrollPrev } =
    useCarousel();

  const handleClick = () => {
    if (buttonType === "next") {
      scrollNext();
    } else {
      scrollPrev();
    }
  };

  return (
    <button
      ref={ref}
      className={cn("m-0 rounded-full p-0", className)}
      disabled={buttonType === "next" ? !canScrollNext : !canScrollPrev}
      onClick={handleClick}
      {...props}
    >
      {children}
      <span className="sr-only">Next slide</span>
    </button>
  );
});
CustomCarouselNavigation.displayName = "CustomCarouselNavigation";

const formatNumber = (num: number): string => {
  return num < 10 ? `0${num}` : `${num}`;
};

export type CarouselProgressProps = {
  className?: string;
  isContrast?: boolean;
};

const CarouselProgress = React.memo(
  ({ className, isContrast }: CarouselProgressProps) => {
    const { scrollProgress, currentIndex, opts, api } = useCarousel();

    const totalSlides = api?.scrollSnapList().length || 0;

    const calculateIndexFromProgress = React.useCallback(
      (progress: number): number => {
        if (totalSlides <= 1) return 0;
        return Math.min(
          Math.floor((progress / 100) * totalSlides),
          totalSlides - 1
        );
      },
      [totalSlides]
    );

    const calculateNormalizedProgress = React.useCallback((): number => {
      if (totalSlides <= 1) return 0;
      return Math.ceil(((currentIndex + 1) / totalSlides) * 100);
    }, [currentIndex, totalSlides]);

    const displayIndex = !opts?.dragFree
      ? currentIndex + 1
      : calculateIndexFromProgress(scrollProgress || 0) + 1;

    const progressWidth = !opts?.dragFree
      ? `${calculateNormalizedProgress()}%`
      : `${scrollProgress}%`;

    return (
      <div
        className={cn(
          "large-3 flex gap-3 bg-transparent font-[400]",
          isContrast ? "text-theme-other-white" : "text-theme-accent-blue",
          className
        )}
      >
        <div className="flex items-center gap-2">
          <span style={{ fontFamily: "var(--font-newyork)" }}>
            {formatNumber(displayIndex)}
          </span>

          <div className="h-1 w-[72px] overflow-hidden bg-transparent/10">
            <div
              className={cn(
                "bg-theme-accent-blue h-full",
                isContrast && "bg-theme-other-white"
              )}
              style={{
                width: progressWidth,
                transition: !opts?.dragFree ? "width 0.5s ease-out" : "width",
              }}
              aria-label="Carousel progress indicator"
            />
          </div>

          <span
            style={{
              fontFamily: "var(--font-newyork)",
            }}
          >
            {formatNumber(totalSlides)}
          </span>
        </div>
      </div>
    );
  }
);

CarouselProgress.displayName = "CarouselProgress";

export {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
  useCarousel,
  CarouselProgress,
  CarouselDots,
};
