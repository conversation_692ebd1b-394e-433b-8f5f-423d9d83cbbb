import React from "react";

import { DividerProps } from "./utils/types";

const Divider: React.FC<DividerProps> = ({
  orientation = "horizontal",
  thickness = "1px",
  color = "#e0e0e0",
  length = "100%",
  margin = "1rem 0",
  padding = "0",
  content,
  contentPosition = "center",
  className = "",
  style = {},
}) => {
  const isVertical = orientation === "vertical";

  const baseStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent:
      contentPosition === "left" || contentPosition === "top"
        ? "flex-start"
        : contentPosition === "right" || contentPosition === "bottom"
          ? "flex-end"
          : "center",
    margin,
    padding,
    flexDirection: isVertical ? "column" : "row",
    ...style,
  };

  const lineStyle: React.CSSProperties = {
    backgroundColor: color,
    flexGrow: 1,
    ...(isVertical
      ? { width: thickness, height: length }
      : { height: thickness, width: length }),
  };

  const contentSpacing = isVertical
    ? { margin: "0.5rem 0" }
    : { margin: "0 0.5rem" };

  return (
    <div className={className} style={baseStyle}>
      <div style={lineStyle} />
      {content && <span style={contentSpacing}>{content}</span>}
      {content && <div style={lineStyle} />}
    </div>
  );
};

export default Divider;
