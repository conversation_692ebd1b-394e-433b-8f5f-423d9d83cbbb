export type DividerProps = {
  orientation?: "horizontal" | "vertical";
  thickness?: string; // e.g. "1px", "2px", "0.5rem"
  color?: string;
  length?: string; // e.g. "100%", "50px", "10rem"
  margin?: string; // shorthand e.g. "1rem 0"
  padding?: string;
  content?: React.ReactNode;
  contentPosition?: "center" | "left" | "right" | "top" | "bottom";
  className?: string;
  style?: React.CSSProperties;
};
