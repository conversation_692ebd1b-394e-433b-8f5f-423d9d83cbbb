"use client";

import React from "react";

import { BlocksRenderer } from "@strapi/blocks-react-renderer";

function RichtextBlockRenderer({ content }: { content: any }) {
  return (
    <BlocksRenderer
      content={content}
      blocks={{
        list: ({ format, children }) => {
          if (format === "unordered")
            return <ul className="list-disc pl-5">{children}</ul>;
          if (format === "ordered")
            return <ol className="list-decimal pl-5">{children}</ol>;
          return <ul className="list-disc pl-5">{children}</ul>;
        },
        heading: ({ level, children }) => {
          if (level === 1) return <h1 className="h1">{children}</h1>;
          if (level === 2) return <h2 className="h2">{children}</h2>;
          if (level === 3) return <h3>{children}</h3>;
          if (level === 4) return <h4>{children}</h4>;
          if (level === 5) return <h5>{children}</h5>;
          if (level === 6) return <h6>{children}</h6>;
          return <p>{children}</p>;
        },
        paragraph: ({ children }) => {
          return <p className="body-copy">{children}</p>;
        },
      }}
    />
  );
}

export default RichtextBlockRenderer;
