"use client";

import React, { useEffect } from "react";

import Heart from "assets/icons/heart";

function LikeButton({
  isLiked = false,
  setIsLiked,
}: {
  isLiked?: boolean;
  setIsLiked?: (x: boolean) => void;
}) {
  const [isClicked, setIsClicked] = React.useState(false);

  useEffect(() => {
    setIsClicked(isLiked);
  }, [isLiked]);

  return (
    <div
      className="cursor-pointer"
      onClick={(e) => {
        e.stopPropagation();
        if (setIsLiked) {
          setIsLiked(!isClicked);
        }
        setIsClicked(!isClicked);
      }}
    >
      <Heart
        fill={isClicked ? "#AA182C" : "#FFF"}
        color={isClicked ? "#AA182C" : "currentColor"}
        width={18}
        height={18}
      />
    </div>
  );
}

export default LikeButton;
