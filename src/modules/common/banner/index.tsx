import cn from "utils/helpers/cn";

import MediaDisplay from "../media-display";
import { BannerProps } from "./utils/types";
import { MediaDisplayProps } from "../media-display/utils/types";

const Banner = ({
  src,
  alt,
  content,
  className,
  applyGradient = false,
  gradientClass = "bg-gradient-to-r from-black/70 via-black/50 to-transparent",
  loading = "lazy",
  ...mediaDisplayProps
}: BannerProps & MediaDisplayProps) => {
  return (
    <div className="relative h-full overflow-hidden">
      {/* Background Image */}
      <MediaDisplay
        className={cn("h-full w-full object-cover object-center", className)}
        src={src}
        alt={alt}
        loading={loading}
        quality={70}
        {...mediaDisplayProps}
      />
      {/* Overlay with gradient */}
      {applyGradient && (
        <div
          className={cn("pointer-events-none absolute inset-0", gradientClass)}
        />
      )}
      {content}
    </div>
  );
};

export default Banner;
