"use client";

import React, { useRef, useMemo, memo, RefObject } from "react";

import Image from "@modules/ui-elements/image";
import cn from "utils/helpers/cn";
import { getMediaType } from "utils/helpers/common";
import useLazyVideo from "utils/hooks/use-lazy-video";

import { MediaDisplayProps } from "./utils/types";
import PlaceholderImage from "../icons/placeholder-image";

const MediaDisplay: React.FC<MediaDisplayProps> = ({
  src,
  className,
  alt = "",
  loading = "lazy",
  sizes = "100vw",
  controls = false,
  autoPlay = true,
  unOptimized = false,
  quality = 80,
  mute = true,
  poster = "",
  loop = true,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const formattedSrc = useMemo(() => {
    if (!src) return "";
    return src.startsWith("http") || src.startsWith("/") ? src : `/${src}`;
  }, [src]);

  const mediaType = useMemo(() => getMediaType(formattedSrc), [formattedSrc]);

  useLazyVideo({
    containerRef: containerRef as RefObject<HTMLDivElement>,
    autoPlay,
    muted: mute,
  });

  if (!formattedSrc) {
    return (
      <div
        className={cn(
          "flex h-full w-full items-center justify-center",
          className
        )}
      >
        <PlaceholderImage size={24} />
      </div>
    );
  }

  if (mediaType === "video") {
    return (
      <div
        ref={containerRef}
        className={cn("relative h-full w-full", className)}
      >
        <video
          ref={videoRef}
          loop={loop}
          playsInline
          preload="none"
          poster={poster}
          className="lazy-video h-full w-full object-cover"
          muted={mute}
          controls={controls}
        >
          <source
            data-src={formattedSrc}
            type={`video/${formattedSrc.split(".").pop()}`}
          />
        </video>
      </div>
    );
  }

  // Render Image or GIF
  return (
    <Image
      src={formattedSrc}
      alt={alt}
      width={0}
      height={0}
      sizes={sizes}
      className={cn("h-auto w-full object-cover", className)}
      loading={loading}
      quality={quality}
      unoptimized={mediaType === "gif" || unOptimized}
    />
  );
};

export default memo(MediaDisplay);
