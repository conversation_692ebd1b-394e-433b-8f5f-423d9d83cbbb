import React from "react";

import { executeQuery } from "utils/helpers/query";
import { getFooterBlockQuery } from "utils/strapi-api/footer-query";

import FooterNavigation from "./components";
import { FooterData } from "./utils/types";

async function Footer() {
  const footerBlockRes: { footer: FooterData } =
    await executeQuery(getFooterBlockQuery);

  let footerBocks = footerBlockRes?.footer;

  return <FooterNavigation footerData={footerBocks} />;
}

export default Footer;
