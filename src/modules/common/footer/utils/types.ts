import { Media, SubMenu, DisplayType } from "utils/types/common";

// Use Media as Logo since they're identical
export type Logo = Media;

export interface FooterNavLink {
  id: string;
  title: string;
  slug: string;
  sub_menu: SubMenu[];
}

export interface LegalLink {
  id: string;
  title: string;
  logo: Logo;
  slug: string;
  display_type: string;
}

export interface SocialLink {
  id: string;
  title: string;
  icon: Logo;
  handle: string;
  display_type: DisplayType;
}

export interface FooterData {
  documentId: string;
  accent_logo: Logo;
  contrast_logo: Logo;
  footer_nav_link: FooterNavLink[];
  legel_links: LegalLink[];
  legel_link_logo: Logo;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  social_links: SocialLink[];
}
