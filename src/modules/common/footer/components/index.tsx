"use client";

import React, { useState } from "react";

import AccordionSmooth from "@modules/common/accordion-smooth";
import Divider from "@modules/common/divider";
import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";
import CopyRight from "assets/icons/copy-right";
import { ChevronDown, ChevronRight, ChevronUp } from "lucide-react";

import EmailDialog from "./email-dialog";
import { FooterData } from "../utils/types";

const FooterSection: React.FC<{
  navItems: FooterData["footer_nav_link"];
  socialLinks: FooterData["social_links"];
}> = ({ navItems, socialLinks }) => {
  const [showEmailSubscribeDialog, setShowEmailSubscribeDialog] =
    useState(false);

  return (
    <>
      <div className="flex w-full flex-col gap-y-1 md:hidden">
        {navItems?.map((nav, index: number) => (
          <React.Fragment key={nav?.id}>
            <AccordionSmooth
              key={nav?.id}
              accordionHeader={
                <Text className="cursor-pointer text-theme-text-contrast body-copy">
                  {nav?.title}
                </Text>
              }
              openIcon={<ChevronUp />}
              closeIcon={<ChevronDown />}
              defaultOpen={false}
              accordionBody={
                <div className="flex flex-col gap-5 pt-5">
                  {nav?.sub_menu &&
                    nav?.sub_menu.map((c) => (
                      <NextLink
                        key={c?.title}
                        className="text-theme-muted body-copy-2 hover:text-theme-text-contrast"
                        href={c?.slug}
                        onClick={() => {
                          if (c?.slug?.startsWith("#")) {
                            setShowEmailSubscribeDialog(true);
                          } else {
                            window.scrollTo({
                              top: 0,
                              behavior: "smooth",
                            });
                          }
                        }}
                      >
                        {c?.title}
                      </NextLink>
                    ))}
                </div>
              }
            />
            {index !== navItems.length - 1 && (
              <Divider className="border-theme-muted" />
            )}
          </React.Fragment>
        ))}
        <SocialLinksSection links={socialLinks} />
      </div>

      <div className="hidden w-full md:block">
        <div className="flex w-full items-start justify-between gap-6">
          {navItems?.map((nav, index: number) => (
            <div key={nav?.id} className="space-y-4">
              <span className="body-copy-semi-bold">{nav?.title}</span>
              <ul className="space-y-3 body-copy-2">
                {nav?.sub_menu?.map((sub) => (
                  <li key={sub?.id}>
                    {sub?.type === "Link" && (
                      <NextLink
                        href={sub?.slug}
                        className="text-theme-muted hover:text-theme-text-contrast"
                      >
                        {sub?.title}
                      </NextLink>
                    )}
                    {sub?.type === "Button" && (
                      <Button
                        className="flex h-5 w-fit flex-row items-center gap-2 border-0 pl-0 hover:bg-transparent hover:text-theme-text-contrast"
                        theme="contrast"
                        onClick={() => {
                          if (sub?.slug?.startsWith("#"))
                            setShowEmailSubscribeDialog(true);
                        }}
                      >
                        <div className="flex flex-row items-center gap-2">
                          {sub?.title}
                          <ChevronRight size={16} />
                        </div>
                      </Button>
                    )}
                  </li>
                ))}
                {index === navItems?.length - 1 && (
                  <SocialLinksSection links={socialLinks} />
                )}
              </ul>
            </div>
          ))}
        </div>
      </div>
      <EmailDialog
        open={showEmailSubscribeDialog}
        handleClose={() => setShowEmailSubscribeDialog(false)}
      />
    </>
  );
};

const SocialLinksSection = ({
  links,
}: {
  links: FooterData["social_links"];
}) => {
  return (
    <div className="flex w-full items-center justify-start">
      {links?.map((link) => (
        <NextLink
          key={link?.id}
          href={link?.handle}
          className="text-theme-muted -ml-2 flex items-center justify-start body-copy-2 hover:text-theme-text-contrast"
        >
          <MediaDisplay
            src={link?.icon?.url}
            className="w-10 object-scale-down"
          />
        </NextLink>
      ))}
    </div>
  );
};

const FooterNavigation = ({ footerData }: { footerData: FooterData }) => {
  return (
    <footer className="w-full bg-theme-background-secondary text-theme-text-contrast">
      <div className="flex flex-col items-center gap-y-10 py-10 lg-container">
        <NextLink href="/">
          <MediaDisplay
            src={footerData?.accent_logo?.url}
            alt={footerData?.accent_logo?.alternativeText || "Footer Logo"}
            className="w-40"
          />
        </NextLink>

        <FooterSection
          navItems={footerData?.footer_nav_link}
          socialLinks={footerData?.social_links}
        />

        <Divider className="border-theme-muted hidden w-full border-b-0 border-t-[0.5px] md:block" />

        {/* Legal Links Section */}
        <div className="w-full lg:pt-4">
          <div className="flex flex-col gap-y-5 lg:flex-row lg:items-center lg:justify-start lg:gap-x-10 lg:gap-y-0">
            <div className="flex items-center gap-1">
              <CopyRight width={15} height={15} />
              <MediaDisplay
                src={footerData?.legel_link_logo?.url}
                className="w-16"
              />
            </div>

            <div className="grid grid-cols-2 gap-4 lg:flex lg:items-center lg:gap-12">
              {footerData?.legel_links?.map((link) => (
                <NextLink
                  key={link?.id}
                  href={link?.slug}
                  className="text-theme-muted flex items-center gap-2 body-copy-2 hover:text-theme-text-contrast"
                >
                  <span>{link?.title}</span>
                </NextLink>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default FooterNavigation;
