"use client";

import { useState } from "react";

import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@modules/common/dialog";
import Button from "@modules/ui-elements/button";
import Input from "@modules/ui-elements/input";
// adjust path if needed

interface EmailDialogProps {
  open: boolean;
  handleClose: () => void;
}

export default function EmailDialog({ open, handleClose }: EmailDialogProps) {
  const [email, setEmail] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleClose();
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && handleClose()}>
      <DialogContent>
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Subscribe</DialogTitle>
            <DialogDescription>
              Enter your email address to subscribe to our newsletter.
            </DialogDescription>
          </DialogHeader>

          <div className="mt-4 flex items-center justify-center gap-5">
            <Input
              type="email"
              required
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full rounded py-2 text-sm focus:outline-none focus:ring"
            />
            <Button className="w-fit" variant="primary" theme="default">
              Submit
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
