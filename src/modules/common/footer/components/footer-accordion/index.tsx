"use client";

import { useState, useRef, useEffect } from "react";

import { ChevronDown } from "lucide-react";
import cn from "utils/helpers/cn";

const FooterAccordion: React.FC<{
  title: string;
  children: React.ReactNode;
  titleClassName?: string;
  defaultOpen?: boolean;
}> = ({ title, children, titleClassName, defaultOpen }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen || false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState("0px");

  useEffect(() => {
    if (contentRef.current) {
      setHeight(isOpen ? `${contentRef.current.scrollHeight}px` : "0px");
    }
  }, [isOpen]);

  const handleClose = () => setIsOpen(false);

  return (
    <div className="w-full">
      <button
        className={cn(
          "flex w-full cursor-pointer items-center justify-between py-3 transition-all duration-300 ease-in-out body-copy body-copy-semi-bold",
          titleClassName
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        {title}
        <ChevronDown
          className={`h-4 w-4 transform transition-transform duration-300 ${
            isOpen ? "rotate-180" : "rotate-0"
          }`}
        />
      </button>
      <div
        ref={contentRef}
        className="motion-safe:animate-slide overflow-hidden transition-[height] duration-300 ease-in-out body-copy-2"
        style={{ height }}
        onClick={handleClose}
      >
        <ul className="text-ui-fg-subtle txt-small grid grid-cols-1 gap-4 py-2">
          {children}
        </ul>
      </div>
    </div>
  );
};

export default FooterAccordion;
