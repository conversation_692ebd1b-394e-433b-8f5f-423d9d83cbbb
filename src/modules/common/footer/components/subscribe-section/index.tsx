"use client";
import React from "react";

import Button from "@modules/ui-elements/button";
import Input from "@modules/ui-elements/input";

const SubscribeSection = () => {
  return (
    <div className="flex w-[90%] flex-col gap-y-2 sm:flex-row sm:gap-x-2 md:w-1/2">
      <Input
        label="Email"
        name="email"
        type="email"
        title="Enter a valid email address."
        autoComplete="email"
        required
        data-testid="email-input"
      />
      <Button className="w-full sm:w-[30%] md:w-[40%] lg:w-[20%]">
        Subscribe
      </Button>
    </div>
  );
};

export default SubscribeSection;
