"use client";

import React, { useEffect, useRef, useState } from "react";

import RoundedMinus from "assets/icons/rounded-minus";
import RoundedPlus from "assets/icons/rounded-plus";
import cn from "utils/helpers/cn";

function AccordionSmooth({
  accordionHeader,
  accordionBody,
  openIcon = <RoundedMinus />,
  closeIcon = <RoundedPlus />,
  className = "",
  defaultOpen,
  accordionBodyClassName,
  accordionHeaderClassName,
  handleAccordionState,
}: {
  accordionHeader: React.ReactElement;
  accordionBody: React.ReactElement;
  openIcon?: React.ReactElement;
  closeIcon?: React.ReactElement;
  className?: string;
  defaultOpen?: boolean;
  accordionHeaderClassName?: string;
  accordionBodyClassName?: string;
  handleAccordionState?: (x: boolean) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState("0px");

  useEffect(() => {
    setIsOpen(defaultOpen || false);
  }, [defaultOpen]);

  useEffect(() => {
    if (contentRef.current) {
      setHeight(isOpen ? `${contentRef.current.scrollHeight}px` : "0px");
    }
  }, [isOpen]);

  return (
    <div
      className={cn(
        "flex w-full flex-col items-start justify-start",
        className
      )}
    >
      <div
        className={cn(
          "flex w-full items-center justify-between gap-2",
          accordionHeaderClassName
        )}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          setIsOpen(!isOpen);
          handleAccordionState?.(!isOpen);
        }}
      >
        {accordionHeader}
        <div
          className="group cursor-pointer transition-transform duration-200"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setIsOpen(!isOpen);
            handleAccordionState?.(!isOpen);
          }}
        >
          <div
            className={`transition-transform duration-300 ${isOpen ? "rotate-360" : "rotate-0"}`}
          >
            {isOpen ? openIcon : closeIcon}
          </div>
        </div>
      </div>
      <div
        ref={contentRef}
        className={cn(
          "w-full overflow-hidden transition-[height] duration-300 ease-in-out",
          accordionBodyClassName
        )}
        style={{ height }}
      >
        {accordionBody}
      </div>
    </div>
  );
}

export default AccordionSmooth;
