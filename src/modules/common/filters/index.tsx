import { filtersDefault } from "./utils/constants";

function Filters() {
  return (
    <div className="flex h-[65px] w-full items-center justify-between px-5 pt-5 sm:px-10 lg:px-20">
      <div className="flex items-center justify-start gap-10 body-copy">
        <p className="text-nowrap uppercase body-copy">Filter By</p>
        {filtersDefault.filters.map((item, index) => {
          return (
            <div
              key={`filter-${index}`}
              className="cursor-pointer body-copy-semi-bold"
            >
              {item.heading}
            </div>
          );
        })}
      </div>
      <div className="flex items-center justify-end gap-2">
        <div>
          <p className="text-theme-text-secondary body-copy">81 Items</p>
        </div>
        <div>
          <p className="text-theme-text-secondary body-copy">|</p>
        </div>
        <div>
          <p className="body-copy-semi-bold">Sort By</p>
        </div>
      </div>
    </div>
  );
}

export default Filters;
