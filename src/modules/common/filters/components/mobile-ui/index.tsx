// src/components/filters/mobile-filter-ui.tsx
import React, { useState } from "react";

import Button from "@modules/ui-elements/button";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, X } from "lucide-react";

import { FILTER_OPTIONS } from "../../utils/constants";
import { FilterKey } from "../../utils/types";

interface MobileFilterUIProps {
  productCount: number;
  filterCount: number;
  openFilter: FilterKey | null;
  toggleFilter: ({ filter }: { filter: FilterKey }) => void;
  handleSelect: (
    filterKey: FilterKey,
    value: string,
    shouldCloseFilter?: boolean
  ) => void;
  clearFilters: () => void;
  getSelectedValues: (filterKey: FilterKey) => string | string[] | undefined;
  isOptionSelected: (filterKey: FilterKey, value: string) => boolean;
  mergedConfig: {
    multiSelect: {
      [key in FilterKey]?: boolean;
    };
  };
  renderFilterOptions: (filterKey: FilterKey) => React.ReactNode;
}

export const MobileFilterUI: React.FC<MobileFilterUIProps> = ({
  productCount,
  filterCount,
  openFilter,
  toggleFilter,
  handleSelect,
  clearFilters,
  getSelectedValues,
  isOptionSelected,
  mergedConfig,
  renderFilterOptions,
}) => {
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState<boolean>(false);
  const [mobileSortOpen, setMobileSortOpen] = useState<boolean>(false);

  return (
    <>
      {/* Mobile Filter Bar - visible only on mobile */}
      <div className="flex items-center justify-between lg:hidden">
        <div className="border-theme-muted flex flex-1 items-center justify-center border-r p-4">
          <button
            className="flex transform items-center gap-1 transition-transform duration-200"
            onClick={() => setIsMobileFilterOpen(true)}
          >
            Filter By
            {filterCount > 0 && (
              <span className="ml-1 origin-center transform rounded-full bg-theme-background-cards px-2 py-1 text-theme-background-accent transition-transform duration-200">
                {filterCount}
              </span>
            )}
          </button>
        </div>
        <div className="flex flex-1 items-center justify-center p-4">
          <button
            className="flex transform items-center gap-1 transition-transform duration-200"
            onClick={() => setMobileSortOpen(true)}
          >
            Sort Options
            <span className="float-right"></span>
          </button>
        </div>
      </div>

      {/* Mobile Filter Modal */}
      <AnimatePresence>
        {isMobileFilterOpen && (
          <motion.div
            initial={{ y: "100%" }}
            animate={{ y: 0 }}
            exit={{ y: "100%" }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="fixed inset-0 z-50 flex flex-col bg-theme-background-primary body-copy-semi-bold lg:hidden"
          >
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.2 }}
              className="border-theme-muted flex items-center justify-between border-b p-4"
            >
              <h2>Filter Options</h2>

              <div className="flex items-center gap-1">
                <div>
                  {productCount} {productCount === 1 ? "Item" : "Items"}
                </div>
                <Button onClick={() => setIsMobileFilterOpen(false)}>
                  <X />
                </Button>
              </div>
            </motion.div>

            <div className="flex-1 overflow-y-auto">
              {Object.keys(FILTER_OPTIONS).map((key) => {
                const filterKey = key as FilterKey;
                if (filterKey === "sort") return null; // Handled in sort modal

                const isMultiSelect = !!mergedConfig.multiSelect[filterKey];
                const selectedValues = getSelectedValues(filterKey);
                const hasSelection = isMultiSelect
                  ? !!selectedValues && (selectedValues as string[]).length > 0
                  : !!selectedValues;

                return (
                  <div key={key} className="border-theme-muted border-b">
                    <div
                      className="active:bg-theme-card-two flex cursor-pointer items-center justify-between p-4"
                      onClick={() => toggleFilter({ filter: filterKey })}
                      role="button"
                      aria-expanded={openFilter === filterKey}
                    >
                      <div className="uppercase">
                        {key.charAt(0).toUpperCase() + key.slice(1)}
                        {hasSelection && (
                          <motion.span
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: "spring", stiffness: 200 }}
                            className="ml-2 rounded-full bg-theme-background-cards px-2 py-1 text-theme-background-accent cta-copy"
                          >
                            {isMultiSelect && Array.isArray(selectedValues)
                              ? selectedValues.length
                              : (selectedValues as string)}
                          </motion.span>
                        )}
                      </div>
                      <motion.div
                        animate={{ rotate: openFilter === filterKey ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronDown />
                      </motion.div>
                    </div>

                    <AnimatePresence>
                      {openFilter === filterKey && (
                        <motion.div
                          initial={{ height: 0, opacity: 0, paddingBottom: 0 }}
                          animate={{
                            height: "auto",
                            opacity: 1,
                          }}
                          exit={{ height: 0, opacity: 0, paddingBottom: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className={`space-y-2 overflow-hidden px-4`}
                          role="listbox"
                        >
                          {renderFilterOptions(filterKey)}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                );
              })}

              {/* Express Delivery Option */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="border-theme-muted flex items-center justify-between border-b p-4"
              >
                <label
                  htmlFor="mobile-express-delivery"
                  className="cursor-pointer"
                >
                  Express Delivery
                </label>
                {/* <Switch
                  id="mobile-express-delivery"
                  className="transition-all duration-200 data-[state=checked]:bg-theme-background-accent"
                /> */}
              </motion.div>
            </div>

            {/* Clear and Apply Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
              className="border-theme-muted flex justify-between border-t p-4"
            >
              <button
                className="text-red-500 transition-transform duration-200"
                onClick={clearFilters}
              >
                Clear All
              </button>
              <button
                className="rounded-md bg-theme-background-secondary px-6 py-2 text-theme-text-contrast transition-transform duration-200"
                onClick={() => setIsMobileFilterOpen(false)}
              >
                Apply
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Sort Modal */}
      <AnimatePresence>
        {mobileSortOpen && (
          <motion.div
            initial={{ y: "100%" }}
            animate={{ y: 0 }}
            exit={{ y: "100%" }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="fixed inset-0 z-50 flex flex-col bg-theme-background-primary body-copy-semi-bold lg:hidden"
          >
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.2 }}
              className="border-theme-muted flex items-center justify-between border-b p-4"
            >
              <h2>Sort Options</h2>

              <div className="flex items-center gap-1">
                <div>{productCount} Items</div>
                <Button onClick={() => setMobileSortOpen(false)}>
                  <X />
                </Button>
              </div>
            </motion.div>

            <div className="flex-1 overflow-y-auto">
              {FILTER_OPTIONS.sort.map((option, index) => (
                <motion.div
                  key={option}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    delay: 0.1 + index * 0.05,
                    duration: 0.2,
                  }}
                  className={`border-theme-muted active:bg-theme-card-two cursor-pointer border-b p-4 transition-all duration-200 ${
                    isOptionSelected("sort", option)
                      ? "bg-theme-card-two"
                      : "hover:bg-theme-card-two"
                  }`}
                  onClick={() => handleSelect("sort", option)}
                  role="option"
                  aria-selected={isOptionSelected("sort", option)}
                >
                  {option}
                  {isOptionSelected("sort", option) && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 200 }}
                      className="float-right"
                    >
                      ✓
                    </motion.span>
                  )}
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
              className="border-theme-muted flex justify-end border-t p-4"
            >
              <button
                className="rounded-md bg-theme-background-secondary px-6 py-2 text-theme-text-contrast transition-transform duration-200"
                onClick={() => setMobileSortOpen(false)}
              >
                Apply
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
