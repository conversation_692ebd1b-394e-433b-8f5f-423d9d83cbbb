import React, { memo } from "react";

import { ChevronDown, X } from "lucide-react";

import { FilterKey } from "../../utils/types";

export const FilterOption = memo(
  ({
    option,
    isSelected,
    onClick,
  }: {
    option: string;
    isSelected: boolean;
    onClick: () => void;
  }) => {
    return (
      <div
        className={`w-fit transform cursor-pointer rounded-md px-3 py-2 transition-all duration-200 body-copy-semi-bold ${
          isSelected && "text-theme-background-accent"
        }`}
        onClick={onClick}
        role="option"
        aria-selected={isSelected}
      >
        {option}
      </div>
    );
  }
);

FilterOption.displayName = "FilterOption";

export const FilterButton = memo(
  ({
    label,
    isOpen,
    hasSelection,
    isMultiSelect,
    selectedValues,
    onClick,
    handleClearFilter,
  }: {
    label: string;
    isOpen: boolean;
    hasSelection: boolean;
    isMultiSelect: boolean;
    selectedValues: string | string[] | undefined;
    handleClearFilter: (filterType: FilterKey) => void;
    onClick: () => void;
  }) => {
    const filterKey = label.toLowerCase() as FilterKey;

    // Convert selected values to array for display
    const selectedArray = selectedValues
      ? Array.isArray(selectedValues)
        ? selectedValues
        : [selectedValues as string]
      : [];

    return (
      <div
        className="flex cursor-pointer items-center gap-1 px-3 py-1"
        onClick={onClick}
        role="button"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        {label}{" "}
        <div
          className="transform transition-transform duration-200"
          style={{ transform: isOpen ? "rotate(180deg)" : "rotate(0deg)" }}
        >
          <ChevronDown />
        </div>
        {/* {hasSelection && (
          <div className="flex items-center gap-1">
            {selectedArray.map((value, index) => (
              <span
                key={`${value}-${index}`}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                className="ml-2 bg-theme-background-cards px-2 py-1 rounded-md flex items-center gap-1 transition-all duration-200 origin-center"
              >
                {value}{" "}
                <div className="transform transition-transform duration-200">
                  <X
                    className="w-3 h-3 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();

                      // For multiselect, we need to handle individual value removal
                      if (isMultiSelect && Array.isArray(selectedValues)) {
                        const updatedValues = selectedValues.filter(
                          (v) => v !== value
                        );
                        if (updatedValues.length === 0) {
                          handleClearFilter(filterKey);
                        }
                      } else {
                        // For single select, we clear the whole filter
                        handleClearFilter(filterKey);
                      }
                    }}
                  />
                </div>
              </span>
            ))}
          </div>
        )} */}
      </div>
    );
  }
);

FilterButton.displayName = "FilterButton";
