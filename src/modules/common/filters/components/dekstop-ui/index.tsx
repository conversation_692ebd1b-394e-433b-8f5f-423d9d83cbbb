import React, { useMemo } from "react";

import Divider from "@modules/common/divider";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown } from "lucide-react";

import { FILTER_OPTIONS } from "../../utils/constants";
import { FilterKey } from "../../utils/types";
import { FilterButton } from "../filter-components";

interface DesktopFilterUIProps {
  productCount: number;
  openFilter: FilterKey | null;
  filterCount: number;
  getSelectedValues: (filterKey: FilterKey) => string | string[] | undefined;
  toggleFilter: ({ filter }: { filter: FilterKey }) => void;
  handleClearFilter: (filterKey: FilterKey) => void;
  clearFilters: () => void;
  singleSelectFilters: Partial<Record<FilterKey, string>>;
  mergedConfig: {
    multiSelect: {
      [key in FilterKey]?: boolean;
    };
  };
  renderFilterOptions: (filterKey: FilterKey) => React.ReactNode;
}

export const DesktopFilterUI: React.FC<DesktopFilterUIProps> = ({
  productCount,
  openFilter,
  filterCount,
  getSelectedValues,
  toggleFilter,
  handleClearFilter,
  clearFilters,
  singleSelectFilters,
  mergedConfig,
  renderFilterOptions,
}) => {
  // Memoize filter buttons
  const filterButtons = useMemo(() => {
    return Object.keys(FILTER_OPTIONS)
      .map((key) => {
        const filterKey = key as FilterKey;
        if (filterKey === "sort") return null; // Handle sort separately

        const label = key.charAt(0).toUpperCase() + key.slice(1);
        const isOpen = openFilter === filterKey;
        const isMultiSelect = !!mergedConfig.multiSelect[filterKey];
        const selectedValues = getSelectedValues(filterKey);
        const hasSelection = isMultiSelect
          ? !!selectedValues && (selectedValues as string[]).length > 0
          : !!selectedValues;

        return (
          <FilterButton
            key={key}
            label={label}
            isOpen={isOpen}
            hasSelection={hasSelection}
            isMultiSelect={isMultiSelect}
            selectedValues={selectedValues}
            onClick={() => toggleFilter({ filter: filterKey })}
            handleClearFilter={handleClearFilter}
          />
        );
      })
      .filter(Boolean);
  }, [
    openFilter,
    getSelectedValues,
    toggleFilter,
    mergedConfig,
    handleClearFilter,
  ]);

  return (
    <>
      <div className="hidden items-center justify-between py-4 body-copy-2 lg:flex">
        <div className="flex items-center gap-4">
          <div>Filter By</div>
          {filterButtons}

          {/* Express Delivery Switch */}
          <label htmlFor="express-delivery" className="cursor-pointer">
            Express Delivery
          </label>
          {/* <Switch
            id="express-delivery"
            className="transition-all duration-200 data-[state=checked]:bg-theme-background-accent"
          /> */}
        </div>

        <div className="flex items-center gap-4">
          <div className="text-theme-text-muted">{productCount} Items</div>
          <Divider orientation="vertical" className="border-theme-border h-4" />
          <div
            className="relative flex transform cursor-pointer items-center gap-1 rounded-md px-3 py-1 transition-colors duration-200"
            onClick={() => toggleFilter({ filter: "sort" })}
          >
            Sort By{" "}
            {singleSelectFilters.sort ? `: ${singleSelectFilters.sort}` : ""}
            <div
              className="transform transition-transform duration-200"
              style={{
                transform:
                  openFilter === "sort" ? "rotate(180deg)" : "rotate(0deg)",
              }}
            >
              <ChevronDown />
            </div>
          </div>

          {filterCount > 0 && (
            <button
              onClick={clearFilters}
              className="transform text-red-500 underline transition-colors duration-200 hover:text-red-600"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      {/* Desktop Expanded Filter Section */}
      <AnimatePresence>
        {openFilter && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="hidden overflow-hidden bg-theme-background-primary md:block"
            role="listbox"
          >
            <motion.div
              initial={{ y: -20 }}
              animate={{ y: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
              className="grid gap-2 p-4"
            >
              {renderFilterOptions(openFilter)}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
