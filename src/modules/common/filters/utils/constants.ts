export const filtersDefault = {
  filters: [
    {
      heading: "COLLECTION",
      items: [
        { title: "FLORACIOUS", link: "/products?collection=floracious" },
        { title: "INCANTE", link: "/products?collection=incante" },
        { title: "ESSENCE", link: "/products?collection=essence" },
      ],
    },
    {
      heading: "METAL",
      items: [
        { title: "Gold", link: "/products?metal=gold" },
        { title: "Rose Gold", link: "/products?metal=rose-gold" },
        { title: "White Gold", link: "/products?metal=white-gold" },
      ],
    },
    {
      heading: "PRICE",
      items: [
        { title: "Under ₹10,000", link: "/products?price=under-10000" },
        { title: "₹10,000 – ₹25,000", link: "/products?price=10000-25000" },
        { title: "Above ₹25,000", link: "/products?price=above-25000" },
      ],
    },
  ],
  toggles: [
    {
      label: "EXPRESS DELIVERY",
      key: "express_delivery",
      default: false,
    },
  ],
};

export const FILTER_OPTIONS: FilterOptions = {
  collection: ["EDEN", "ESSENCE", "CADENZA"],
  metal: ["Gold", "Silver", "Platinum"],
  price: ["Under $500", "$500 - $1000", "Above $1000"],
  sort: [
    "Price: Low to High",
    "Price: High to Low",
    "Newest First",
    "Best Selling",
  ],
};

export interface FilterOptions {
  collection: string[];
  metal: string[];
  price: string[];
  sort: string[];
  [key: string]: string[];
}
