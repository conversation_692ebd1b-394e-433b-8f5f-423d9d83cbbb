import { Media, SubMenu, DisplayType } from "utils/types/common";

export interface NavActionLink {
  id: string;
  title: string;
  logo: Media;
  slug: string;
  display_type: DisplayType;
  accent_logo: Media;
  contrast_logo: Media;
}

export interface MainNavigation {
  id: string;
  title: string;
  slug: string;
  sub_menu: SubMenu[];
  menu_media: MenuMedia;
}

export interface MenuMedia {
  id: string;
  title: string;
  subtitle: string;
  cta_title: string;
  cta_link: string;
  desktop_media: Media;
  mobile_media: Media;
  description: string;
  responsive: string;
}

export interface HeaderDataResponse {
  documentId: string;
  nav_action_links: NavActionLink[];
  title: string;
  main_navigation: MainNavigation[];
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  accent_logo: Media;
  contrast_logo: Media;
}
