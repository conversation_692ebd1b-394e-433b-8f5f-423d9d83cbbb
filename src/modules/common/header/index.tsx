import { executeQuery } from "utils/helpers/query";
import { getHeaderBlocksQuery } from "utils/strapi-api/header-query";

import HeaderNavigationDesktop from "./components/header-navigation-desktop";
import HeaderNavigationMobile from "./components/header-navigation-mobile";
import { HeaderDataResponse } from "./utils/type";

async function HeaderNavigation() {
  const { header }: { header: HeaderDataResponse } = await executeQuery(
    getHeaderBlocksQuery,
    { cache: "force-cache" }
  );

  return (
    <div className="relative">
      <HeaderNavigationDesktop header={header} />
      <HeaderNavigationMobile header={header} />
    </div>
  );
}

export default HeaderNavigation;
