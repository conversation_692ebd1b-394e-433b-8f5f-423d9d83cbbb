"use client";

import { useEffect, useMemo, useState } from "react";

import { usePathname } from "next/navigation";

import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuViewport,
} from "@modules/ui-elements/navigation-menu";

import {
  HeaderDataResponse,
  MainNavigation,
  NavActionLink,
} from "../../utils/type";
import HeaderLogo from "../header-logo";
import NavigationHeaderMenu from "../navigation-header-menu";

function HeaderNavigationDesktop({ header }: { header: HeaderDataResponse }) {
  const [isFixedOrSticky, setIsFixedOrSticky] = useState("fixed");

  const pathname = usePathname();

  useEffect(() => {
    setIsFixedOrSticky(pathname === "/" ? "fixed" : "sticky");
  }, [pathname]);

  const navigationData = header?.main_navigation || [];
  const navigationActionLinks = header?.nav_action_links || [];

  const [isScrolled, setIsScrolled] = useState(false);

  const handleScroll = () => {
    setIsScrolled(window.scrollY > 40);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const logoObjKey = useMemo(() => {
    if (isFixedOrSticky === "sticky") return "accent_logo";
    return isScrolled ? "accent_logo" : "contrast_logo";
  }, [isScrolled, isFixedOrSticky]);

  const getBackgroundClass = () => {
    if (isFixedOrSticky === "sticky") return "bg-white";
    return isScrolled ? "bg-white" : "bg-transparent";
  };

  const getContrastText = () => {
    if (isFixedOrSticky === "sticky") return false;
    return !isScrolled;
  };

  return (
    <>
      <div
        className={`fixed top-0 z-50 flex ${!getContrastText() ? "h-[79px]" : "h-[161px]"} w-full items-center ${getBackgroundClass()} hidden transition-height duration-300 ease-linear lg:flex`}
      >
        <NavigationMenu className="center hidden w-full items-center justify-between lg:flex">
          <HeaderLogo header={header} isAccentLogo={!getContrastText()} />
          <div className="absolute inset-0 flex items-center justify-center">
            <NavigationMenuList className="flex w-full items-center gap-3 xl:gap-10">
              {navigationData.map((navItem: MainNavigation) => (
                <NavigationHeaderMenu
                  key={navItem?.id}
                  menuData={navItem}
                  isContrastText={getContrastText()}
                />
              ))}
            </NavigationMenuList>
          </div>
          <NavigationMenuList className="mr-5 flex w-[140px] items-center justify-between 2xl:mr-10">
            {navigationActionLinks?.map((navItem: NavActionLink) => {
              if (["both", "desktop"].includes(navItem?.display_type))
                return (
                  <NextLink href={navItem.slug} key={navItem?.id}>
                    <MediaDisplay src={navItem?.[logoObjKey]?.url} />
                  </NextLink>
                );
            })}
          </NavigationMenuList>
          <NavigationMenuViewport />
        </NavigationMenu>
      </div>
      {pathname !== "/" && <div className="h-[79px]" />}
    </>
  );
}

export default HeaderNavigationDesktop;
