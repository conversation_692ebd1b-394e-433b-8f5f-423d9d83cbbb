import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import Button from "@modules/ui-elements/button";
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@modules/ui-elements/navigation-menu";
import "./index.css";

import { MainNavigation } from "../../utils/type";

const NavigationHeaderMenu = ({
  menuData,
  isContrastText,
}: {
  menuData: MainNavigation;
  isContrastText: boolean;
}) => {
  // Check if the menu has submenus
  const hasSubMenu = menuData?.sub_menu && menuData?.sub_menu?.length > 0;

  const MenuItemWrapper = hasSubMenu ? NavigationMenuTrigger : "div";

  return (
    <NavigationMenuItem className="relative w-full">
      <MenuItemWrapper
        className={`menu-item cursor-pointer uppercase ${isContrastText && "text-theme-text-contrast"} ${navigationMenuTriggerStyle()} h-16 font-semibold`}
      >
        <NextLink href={menuData?.slug}>{menuData?.title}</NextLink>
      </MenuItemWrapper>

      {/* Only render this if sub-menu exists */}
      {hasSubMenu && (
        <NavigationMenuContent className="flex w-full items-start justify-between gap-20 pt-16">
          <div className="flex h-96 gap-5 px-4 xl:gap-20">
            {menuData?.sub_menu?.map((subMenu) => (
              <div key={subMenu?.id}>
                <NavigationMenuLink
                  href={subMenu?.slug}
                  className="font-semibold text-theme-text-primary h2"
                >
                  {subMenu?.title}
                </NavigationMenuLink>

                {/* Render nested sub-menu if it exists */}
                {subMenu?.nested_sub_menu &&
                  subMenu?.nested_sub_menu?.length > 0 && (
                    <div className="">
                      {subMenu?.nested_sub_menu?.map((nestedSubMenu) => (
                        <NavigationMenuLink
                          key={nestedSubMenu?.id}
                          href={nestedSubMenu?.slug}
                          className="text-theme-text-primary body-copy"
                        >
                          {nestedSubMenu?.title}
                        </NavigationMenuLink>
                      ))}
                    </div>
                  )}
              </div>
            ))}
          </div>
          <div className="relative">
            <div className="absolute inset-0 bottom-36 z-50 flex flex-col items-center justify-end text-theme-text-contrast">
              <p className="text-[30px] h2">{menuData?.menu_media?.title}</p>
              <p className="w-3/5 text-center body-copy">
                {menuData?.menu_media?.subtitle}
              </p>
              {menuData?.menu_media?.cta_title && (
                <Button className="mt-3 w-fit" theme="contrast">
                  {menuData?.menu_media?.cta_title}
                </Button>
              )}
            </div>
            {menuData?.menu_media?.desktop_media?.url && (
              <MediaDisplay
                src={menuData?.menu_media?.desktop_media?.url}
                className="-mb-1 h-[400px] w-[400px] xl:h-[445px] xl:w-[465px]"
                loading="lazy"
              />
            )}
          </div>
        </NavigationMenuContent>
      )}
    </NavigationMenuItem>
  );
};

export default NavigationHeaderMenu;
