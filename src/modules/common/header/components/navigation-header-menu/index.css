/* 1. Remove the default text-decoration */
.menu-item {
  position: relative;
  text-decoration: none; /* turn off built-in underline */
}

/* 2. Create a pseudo-element at the bottom of the link */
.menu-item::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 12px; /* adjust to sit just under the text */
  height: 3px; /* same as your textDecorationThickness */
  width: 0; /* start collapsed */
  background-color: var(--bg-accent);
  transition: width 0.3s ease;
}

/* 3. On hover (or focus), expand the pseudo-element to full width */
.menu-item:hover::after,
.menu-item:focus::after {
  width: 100%;
}
