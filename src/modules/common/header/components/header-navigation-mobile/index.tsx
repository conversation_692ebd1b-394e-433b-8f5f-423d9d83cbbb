"use client";

import { useEffect, useMemo, useState } from "react";

import { usePathname } from "next/navigation";

import Divider from "@modules/common/divider";
import MediaDisplay from "@modules/common/media-display";
import NextLink from "@modules/common/next-link";
import Button from "@modules/ui-elements/button";
import Input from "@modules/ui-elements/input";
import {
  NavigationMenuList,
  NavigationMenuViewport,
} from "@modules/ui-elements/navigation-menu";
import { NavigationMenu } from "@radix-ui/react-navigation-menu";
import Search from "assets/icons/search";
import { Menu, X } from "lucide-react";
import cn from "utils/helpers/cn";

import MobileNavigation from "./components/nav-links";
import { HeaderDataResponse, NavActionLink } from "../../utils/type";

function HeaderNavigationMobile({ header }: { header: HeaderDataResponse }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigationActionLinks = header?.nav_action_links || [];

  const navigationData = header?.main_navigation || [];

  const [isScrolled, setIsScrolled] = useState(false);

  const pathname = usePathname();

  const handleScroll = () => {
    setIsScrolled(window.scrollY > 40);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const logoObjKey = useMemo(() => {
    return isScrolled ? "accent_logo" : "contrast_logo";
  }, [isScrolled]);

  return (
    <div className="block lg:hidden">
      <div
        className={`fixed top-0 z-50 flex w-full items-center bg-white transition-bg duration-200 lg:hidden`}
      >
        <NavigationMenu className="relative flex h-[85px] w-full items-center justify-between px-2 sm:px-5 lg:hidden">
          <Button
            className={`relative z-10 w-fit border-0 p-2 text-black hover:bg-transparent hover:${isScrolled ? "text-black" : "text-white"}`}
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle menu"
          >
            <Menu />
          </Button>
          <div className="absolute inset-0 flex items-center justify-center">
            <NextLink href="/" className="z-50 cursor-pointer">
              <MediaDisplay
                className={cn("w-32 object-cover object-center")}
                src={header?.accent_logo?.url}
                alt={header?.[logoObjKey]?.alternativeText}
                loading={"eager"}
                quality={100}
              />
            </NextLink>
          </div>
          <NavigationMenuList className="flex items-center justify-end gap-3 pr-2 sm:gap-6">
            {navigationActionLinks?.map((navItem: NavActionLink) => {
              if (["both", "phone"].includes(navItem?.display_type))
                return (
                  <NextLink href={navItem.slug} key={navItem?.id}>
                    <MediaDisplay
                      src={navItem?.accent_logo?.url}
                      className="w-4"
                    />
                  </NextLink>
                );
            })}
          </NavigationMenuList>
          <NavigationMenuViewport />
          <div
            className={`fixed inset-0 top-0 z-50 w-full transform overflow-y-auto bg-white transition-transform duration-300 ${isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"}`}
          >
            <nav className="h-max space-y-10 p-5 text-theme-text-primary">
              <div className="flex items-center justify-end">
                <Button
                  onClick={() => setIsMobileMenuOpen(false)}
                  variant="primary"
                  className="w-fit border-0 p-1 hover:bg-transparent hover:text-black"
                >
                  <X />
                </Button>
              </div>
              <div className="w-full space-y-5">
                <div className="border-theme-border flex w-full items-center gap-1.5">
                  <Search height={16} width={16} />
                  <Input
                    inputMode="search"
                    name="search"
                    placeholder="What are you looking today?"
                    className="h-6 w-full border-0"
                    wrapperClassName="w-full"
                  />
                </div>
                <Divider className="border-theme-border" />
              </div>
              <div className="relative">
                <MobileNavigation
                  links={navigationData}
                  isMenuOpen={isMobileMenuOpen}
                  closeMenu={() => setIsMobileMenuOpen(false)}
                />
              </div>
              <Divider className="border-theme-border" />
              <div className="flex flex-col justify-center space-y-3.5 text-theme-text-primary body-copy">
                {navigationActionLinks?.map((navItem: NavActionLink) => {
                  return (
                    <NextLink
                      href={navItem?.slug}
                      key={navItem?.id}
                      className="flex items-center gap-3"
                    >
                      <MediaDisplay src={navItem?.logo?.url} className="w-4" />{" "}
                      <span className="body-copy">{navItem?.title}</span>
                    </NextLink>
                  );
                })}
              </div>
            </nav>
          </div>
        </NavigationMenu>
      </div>
      {pathname === "/" && <div className="h-[85px] lg:h-0" />}
    </div>
  );
}

export default HeaderNavigationMobile;
