import React from "react";

import <PERSON> from "next/link";

const NextLink = ({
  children,
  href,
  prefetch = false,
  ...props
}: {
  children?: React.ReactNode;
  href: string;
  className?: string;
  onClick?: () => void;
  passHref?: true;
  prefetch?: boolean;
  [x: string]: unknown;
}) => {
  return (
    <Link href={href} prefetch={prefetch} {...props}>
      {children}
    </Link>
  );
};

export default NextLink;
