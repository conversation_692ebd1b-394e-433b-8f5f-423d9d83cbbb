/* Accordion Slide Down */
@keyframes animate-accordion-down {
  0% {
    max-height: 0;
    /* opacity: 0; */
  }
  100% {
    max-height: 500px; /* You can adjust this based on your content height */
    /* opacity: 1; */
  }
}

/* Accordion Slide Up */
@keyframes animate-accordion-up {
  0% {
    max-height: 500px; /* Match this value with the slide-down animation's max-height */
    /* opacity: 1; */
  }
  100% {
    max-height: 0;
    /* opacity: 0; */
  }
}

/* Apply the animations */
.accordion-content[data-state="open"] {
  animation: animate-accordion-down 0.3s ease-in-out;
}

.accordion-content[data-state="closed"] {
  animation: animate-accordion-up 0.3s ease-in-out;
}
