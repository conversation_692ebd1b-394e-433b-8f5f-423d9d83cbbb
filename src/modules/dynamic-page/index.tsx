import { Suspense } from "react";

import { notFound } from "next/navigation";

import { getTemplateBlocks } from "utils/api/strapi-api";
import renderBlock from "utils/mappers/component-renderer";
import { getTemplateBlocksQuery } from "utils/strapi-api/page-template-query";

import { DynamicPageProps } from "./utils/types";

const DynamicPage: React.FC<DynamicPageProps> = async ({
  page,
  page_type,
  template_name,
}) => {
  const blocks = await getTemplateBlocks(template_name, getTemplateBlocksQuery);

  if (blocks.length === 0) notFound();

  const rendered_blocks = blocks.map((block, index) =>
    renderBlock(block, index, page, page_type)
  );

  return <Suspense>{rendered_blocks}</Suspense>;
};
export default DynamicPage;
