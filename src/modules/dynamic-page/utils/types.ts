import { PageTypeEnum } from "utils/types/common";

import { component_map } from "..";

export interface DynamicPageProps {
  page: string;
  page_type: PageTypeEnum;
  template_name: string;
  location?: string;
}

export interface DynamicPageComponentProps<T> {
  block: T;
  page: string;
  page_type: PageTypeEnum;
  location?: string;
  isFirst: boolean;
  children?: React.ReactNode;
}

export interface DynamicPageBlock {
  __typename: keyof typeof component_map;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}
