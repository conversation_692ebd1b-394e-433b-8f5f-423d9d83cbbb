import React from "react";

import MediaDisplay from "@modules/common/media-display";

import { SizeGuideHeroBlock } from "./utils/types";

function SizeGuideHero({
  block,
  template,
}: {
  block: SizeGuideHeroBlock;
  template: string;
}) {
  const bannerMediaData = block?.hero?.store_banner?.media?.[0];

  const bgImage = bannerMediaData.desktop_media;

  const isTemplateRing = template === "ring";

  const theme = bannerMediaData.theme ?? "light";

  return (
    <div className="relative w-full">
      {bgImage?.url && (
        <MediaDisplay
          src={bgImage?.url}
          className="h-[581px] w-full object-[85%_70%] lg:h-[481px] lg:object-center"
        />
      )}

      <div className="absolute inset-0 left-0 flex h-full flex-col items-start justify-start gap-5 pt-32 lg:left-[13%] lg:justify-center lg:gap-2 lg:pb-10 lg:pt-0">
        {bannerMediaData?.title && (
          <p
            className={`w-full text-center h1 lg:w-1/2 lg:text-left xl:w-[34%] ${theme === "dark" ? "text-theme-text-contrast" : !isTemplateRing && "text-theme-text-contrast md:text-black"} `}
          >
            {bannerMediaData?.title}
          </p>
        )}
        {bannerMediaData?.subtitle && (
          <p
            className={`w-full text-center body-copy lg:text-left ${theme === "dark" ? "text-theme-text-contrast" : !isTemplateRing && "text-theme-text-contrast md:text-black"}`}
          >
            {bannerMediaData?.subtitle}
          </p>
        )}
        {bannerMediaData?.description && (
          <p
            className={`flex w-full justify-center text-center body-copy lg:w-[34%] lg:text-left ${theme === "dark" ? "text-theme-text-contrast" : !isTemplateRing && "text-theme-text-contrast md:text-black"}`}
          >
            <span className="w-[90%] lg:w-full">
              {bannerMediaData?.description}
            </span>
          </p>
        )}
      </div>
    </div>
  );
}

export default SizeGuideHero;
