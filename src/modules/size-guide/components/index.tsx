import { Suspense } from "react";

import { notFound } from "next/navigation";

import { DynamicPageProps } from "@modules/dynamic-page/utils/types";
import { getTemplateBlocks } from "utils/api/strapi-api";
import renderBlock from "utils/mappers/component-renderer";
import { getSizeGuidePageTemplateBlocksQuery } from "utils/strapi-api/size-guide-template-query";

import SizeGuideContent from "./size-guide-content";

async function DynamicSizeGuidePage({
  page,
  page_type,
  template_name,
}: DynamicPageProps) {
  const blocks = await getTemplateBlocks(
    template_name,
    getSizeGuidePageTemplateBlocksQuery
  );
  if (blocks.length === 0) notFound();

  const template = template_name.split("_")[0];

  const sidebar = blocks.find(
    (item) => item.__typename === "ComponentSizeGuideSidebar"
  );

  const guideContent = blocks.find(
    (item) => item.__typename === "ComponentSizeGuideSizeGuideContent"
  );

  const rendered_blocks = blocks.map((block, index) =>
    renderBlock(block, index, page, page_type, template)
  );

  rendered_blocks.splice(
    1,
    0,
    <SizeGuideContent
      sidebar={sidebar}
      key="static-block"
      guideContent={guideContent}
      template={template}
    />
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicSizeGuidePage;
