"use client";

import React, { useEffect } from "react";

import { usePathname } from "next/navigation";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@modules/common/dropdown";
import NextLink from "@modules/common/next-link";
import { sizeGuideCategories } from "@modules/size-guide/data";
import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import ArrowDown from "assets/icons/arrow-down";
import cn from "utils/helpers/cn";

function SizeGuideCategories() {
  const [selectedCategory, setSelectedCategory] =
    React.useState<string>("Ring");

  const path = usePathname();

  useEffect(() => {
    const cat = sizeGuideCategories.find((c) => c.href === path);

    if (cat) {
      setSelectedCategory(cat.title);
    }
  }, [path]);

  return (
    <div className="flex w-full items-center justify-end">
      <div className="hidden w-[calc(100vw-670px)] items-center justify-start gap-20 pb-5 lg:flex">
        {sizeGuideCategories?.map((cat, index) => {
          return (
            <NextLink
              href={cat.href}
              key={`cat-${index}`}
              className={cn(
                "cursor-pointer text-base body-copy",
                `${selectedCategory === cat.title ? "underline decoration-theme-background-accent decoration-2 underline-offset-[10px]" : ""}`,
                "decoration-theme-background-accent decoration-2 underline-offset-[10px] transition-all hover:underline"
              )}
            >
              {cat.title}
            </NextLink>
          );
        })}
      </div>
      <div className="w-full lg:hidden">
        <DropdownMenu>
          <DropdownMenuTrigger className="btn flex h-[42px] w-full items-center justify-center gap-2 border-b border-b-theme-background-container body-copy focus:outline-none">
            Choose a Topic <ArrowDown height={10} />
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-full bg-white" side="bottom">
            {sizeGuideCategories?.map((topic, index: number) => (
              <React.Fragment key={topic.id}>
                <DropdownMenuItem
                  className={cn(
                    "flex w-screen cursor-pointer justify-center py-2 body-copy-semi-bold hover:outline-none",
                    selectedCategory === topic.title &&
                      "text-theme-text-secondary"
                  )}
                >
                  <NextLink href={topic.href}>{topic.title}</NextLink>
                </DropdownMenuItem>
                {index !== sizeGuideCategories?.length - 1 && (
                  <DropdownMenuSeparator className="bg-theme-background-container" />
                )}
              </React.Fragment>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

export default SizeGuideCategories;
