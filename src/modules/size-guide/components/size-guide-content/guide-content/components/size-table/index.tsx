import React from "react";

import {
  <PERSON>H<PERSON>er,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Table,
} from "@modules/common/table";
import Text from "@modules/ui-elements/text";

import { SizeContent } from "../../../utils/types";

function SizeTable({ table }: { table: SizeContent }) {
  const Cols = table?.Cols;

  const tableName = table.table_title;

  const rowCount = Cols?.length > 0 ? Cols?.[0]?.column_row?.length : 0;

  return (
    <>
      {tableName && (
        <Text as="p" size="body_medium">
          {tableName}
        </Text>
      )}
      <Table>
        <TableHeader>
          <TableRow>
            {Cols.map((col) => (
              <TableHead className="text-center body-copy" key={col.id}>
                {col?.column_title?.trim()}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {[...Array(rowCount)].map((_, rowIndex) => (
            <TableRow key={rowIndex}>
              {Cols.map((col) => (
                <TableCell key={col.id} className="text-center body-copy">
                  {col.column_row[rowIndex]?.row_item || ""}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </>
  );
}

export default SizeTable;
