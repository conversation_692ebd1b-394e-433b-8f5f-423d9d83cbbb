import { Media } from "utils/types/common";

interface MediaItemType {
  id: string;
  title: string;
  subtitle: string | null;
  cta_title: string | null;
  cta_link: string | null;
  desktop_media: Media;
  mobile_media: Media;
  description: string | null;
  responsive: string;
}

interface DescriptionChildType {
  text: string;
  type: string;
}

interface DescriptionType {
  type: string;
  children: DescriptionChildType[];
}

interface ActionButtonType {
  id: string;
  title: string;
  href: string;
}

export interface ComponentSizeGuideSidebarType {
  id: string;
  heading: string;
  __typename: string;
  media: MediaItemType[];
  description: DescriptionType[];
  action_buttons: ActionButtonType[];
}

export interface TypeGuideContent {
  type: string;
  level?: number;
  children: Array<{
    text: string;
    type: string;
    bold?: boolean;
  }>;
}

export interface ColumnRow {
  id: string;
  row_item: string;
}

export interface Col {
  id: string;
  column_title: string | null;
  column_row: ColumnRow[];
}

export interface SizeContent {
  id: string;
  Cols: Col[];
  table_title: string | null;
}

export interface ProductImageConnection {
  nodes: Media[];
}

export interface ComponentSizeGuideSizeGuideContent {
  id: string;
  __typename: string;
  product_image_connection: ProductImageConnection;
  product_image: Media[];
  size_content: SizeContent[];
  guide_content: TypeGuideContent[];
}
