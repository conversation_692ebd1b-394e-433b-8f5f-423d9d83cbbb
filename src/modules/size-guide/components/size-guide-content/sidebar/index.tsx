import React from "react";

import MediaDisplay from "@modules/common/media-display";
import RichtextBlockRenderer from "@modules/common/richtext-block-renderer";
import { Carousel } from "@modules/home/<USER>/trending-section/components/custom-carousel";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { ComponentSizeGuideSidebarType } from "../utils/types";

function Sidebar({ sidebar }: { sidebar: ComponentSizeGuideSidebarType }) {
  const product_medias = sidebar?.media;

  const actionButtons = sidebar?.action_buttons;

  return (
    <div className="flex w-full min-w-[300px] flex-col items-center justify-start gap-5 bg-theme-background-cards p-5 lg:min-h-[562px] lg:w-[369px]">
      {product_medias?.length > 0 && (
        <Carousel
          autoPlayInterval={300}
          showDots={false}
          showArrows={true}
          slidesToShow={1}
          centerMode={true}
          autoPlay={product_medias?.length > 1}
        >
          {product_medias?.map((product) => (
            <MediaDisplay
              src={product.desktop_media.url}
              alt={product.title}
              className="h-[333px] w-full object-cover shadow-lg transition-all duration-500"
              key={`product-${product?.id}`}
            />
          ))}
        </Carousel>
      )}
      {sidebar?.heading && (
        <Text as="p" size="body_semi_bold" className="w-full text-left">
          {sidebar?.heading}
        </Text>
      )}
      {sidebar?.description && (
        <div className="w-full break-words text-left body-copy">
          <RichtextBlockRenderer content={sidebar?.description} />
        </div>
      )}
      {actionButtons?.length > 0 && (
        <div className="flex w-full flex-col items-start justify-start gap-3">
          {actionButtons?.map((btn, index) => {
            return (
              <Button className="w-fit" key={index}>
                {btn?.title}
              </Button>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default Sidebar;
