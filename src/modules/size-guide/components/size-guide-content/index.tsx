import GuideContent from "./guide-content";
import SizeGuideCategories from "./guide-content/components/categories";
import Sidebar from "./sidebar";
import {
  ComponentSizeGuideSidebarType,
  ComponentSizeGuideSizeGuideContent,
} from "./utils/types";

function SizeGuideContent({
  sidebar,
  guideContent,
  template,
}: {
  sidebar: ComponentSizeGuideSidebarType;
  guideContent: ComponentSizeGuideSizeGuideContent;
  template: string;
}) {
  return (
    <div className="p-0 lg:p-10 xl:px-40 xl:py-10">
      <div className="hidden w-full lg:block">
        <SizeGuideCategories />
      </div>
      <div className="flex w-full flex-col items-start justify-between gap-5 lg:flex-row lg:gap-5">
        <Sidebar sidebar={sidebar} />
        <GuideContent guideContent={guideContent} template={template} />
      </div>
    </div>
  );
}

export default SizeGuideContent;
