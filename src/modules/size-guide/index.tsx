import React from "react";

import { notFound } from "next/navigation";

import { getPageTemplateName } from "utils/api/strapi-api";

import DynamicSizeGuidePage from "./components";

async function SizeGuideModule({ handle }: { handle: string }) {
  const page = `size-guide/${handle}`;

  const [{ template_name, page_type }] = await Promise.all([
    getPageTemplateName(page),
  ]);

  if (!template_name || !page_type) {
    notFound();
  }

  return (
    <DynamicSizeGuidePage
      page={page}
      page_type={page_type}
      template_name={template_name}
    />
  );
}

export default SizeGuideModule;
