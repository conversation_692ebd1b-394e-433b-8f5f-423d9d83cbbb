import React, { FC } from "react";

import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";

import { BrandTaglineProps } from "./utils/types";
import Text from "@modules/ui-elements/text";

const BrandTagline: FC<DynamicPageComponentProps<BrandTaglineProps>> = ({
  block,
}) => {
  const { brand_tagline } = block;

  const { title, subtitle, description } = brand_tagline;

  return (
    <div className="flex flex-col items-center justify-center bg-theme-background-primary py-14 text-center content-container md:py-20">
      <Text
        as="h1"
        size="display"
        className="text-theme-text-primary lg:max-w-[31.5%]"
      >
        {title} {subtitle}
      </Text>
      <Text
        as="p"
        size="body_large"
        className="text-theme-text-secondary lg:max-w-[41%]"
      >
        {description}
      </Text>
    </div>
  );
};

export default BrandTagline;
