import React from "react";

import { notFound } from "next/navigation";

import { getPageTemplateName } from "utils/api/strapi-api";
import { PageTypeEnum } from "utils/types/common";

import DynamicPage from "../dynamic-page";

const Home = async () => {
  const page = "home";

  const [{ template_name, page_type }] = await Promise.all([
    getPageTemplateName(page),
  ]);

  if (!template_name || !page_type) {
    console.warn(`-> No template name found for page: ${page}`);
    notFound();
  }

  return (
    <DynamicPage
      page={page}
      page_type={PageTypeEnum.Home}
      template_name={template_name}
    />
  );
};

export default Home;
