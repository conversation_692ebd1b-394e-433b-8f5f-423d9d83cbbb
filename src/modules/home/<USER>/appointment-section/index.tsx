import React, { <PERSON> } from "react";

import Link from "next/link";

import Banner from "@modules/common/banner";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Button from "@modules/ui-elements/button";

import { BookAppointmentSectionProps } from "./utils/types";
import Text from "@modules/ui-elements/text";

const BookAppointmentSection: FC<
  DynamicPageComponentProps<BookAppointmentSectionProps>
> = ({ block }) => {
  const { appointment_section } = block;

  return (
    <div className="my-[79px] grid grid-cols-1 gap-14 content-container md:my-[83px] md:grid-cols-2 md:gap-0">
      {appointment_section.media.map((item, index) => (
        <Banner
          key={index}
          src={item.desktop_media.url}
          alt={item.title}
          className="h-[790px] md:h-[700px]"
          content={
            <div className="mt-8 text-theme-text-primary">
              <Text as="h1" size="display">
                {item.title}
              </Text>
              <Text
                as="p"
                size="body_large"
                className="max-w-[90%] text-theme-text-primary"
              >
                {item.description}
              </Text>
              <Link
                href={item.cta_link || "/"}
                passHref
                className="mt-3 block sm:mt-6"
              >
                <Button className="h-[38px] w-fit">{item.cta_title}</Button>
              </Link>
            </div>
          }
        />
      ))}
    </div>
  );
};

export default BookAppointmentSection;
