import React, { <PERSON> } from "react";

import Link from "next/link";

import Banner from "@modules/common/banner";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";

import { PromotionalBannerProps } from "./utils/types";

const PromotionalBanner: FC<
  DynamicPageComponentProps<PromotionalBannerProps>
> = ({ block }) => {
  const { promotional_banner } = block;

  return (
    <Link href={promotional_banner.media[0].cta_link || "/"} passHref>
      <Banner
        src={promotional_banner.media[0].desktop_media.url}
        alt={promotional_banner.media[0].title}
        className="h-screen"
        autoPlay
        loop
      />
    </Link>
  );
};

export default PromotionalBanner;
