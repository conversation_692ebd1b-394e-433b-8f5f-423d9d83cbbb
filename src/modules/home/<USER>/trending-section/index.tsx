import { FC } from "react";

import MediaDisplay from "@modules/common/media-display";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Text from "@modules/ui-elements/text";

import { Carousel } from "./components/custom-carousel";
import { TrendingSectionProps } from "./utils/types";

const TrendingSection: FC<DynamicPageComponentProps<TrendingSectionProps>> = ({
  block,
}) => {
  const { trending_media } = block;

  return (
    <div className="bg-theme-background-primary content-container">
      <div className="py-16 text-center md:py-20">
        <Text
          as="h2"
          size="display"
          className="uppercase text-theme-text-primary"
        >
          Trending
        </Text>
      </div>
      <Carousel
        autoPlayInterval={300}
        showDots={false}
        showArrows={true}
        slidesToShow={3}
        centerMode={true}
        isScale={true}
      >
        {trending_media.map((product) => (
          <div
            key={product.id}
            className="flex h-full cursor-pointer flex-col items-center"
          >
            <div className="relative h-full w-full overflow-hidden">
              <MediaDisplay
                src={product.desktop_media.url}
                alt={product.title}
                className="aspect-[3/4] object-cover shadow-lg transition-all duration-500"
              />
            </div>
            <div className="mt-5 w-[280px] text-left md:w-full">
              <Text
                as="h3"
                size="headline"
                className="text-[36px] font-bold uppercase tracking-wider md:text-[40px]"
              >
                {product.title}
              </Text>
              <Text as="p" size="body_large" className="my-2 text-sm body-copy">
                {product.description}
              </Text>
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default TrendingSection;
