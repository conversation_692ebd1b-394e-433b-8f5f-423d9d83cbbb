"use client";

import { useCallback, useEffect, useState, useRef, ReactNode } from "react";

import AutoScroll from "embla-carousel-auto-scroll";
import useEmblaCarousel from "embla-carousel-react";
import cn from "utils/helpers/cn";

const Next = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="h-8 w-8"
    >
      <path d="M9 18l6-6-6-6" />
    </svg>
  );
};

const Previous = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="h-8 w-8"
    >
      <path d="M15 18l-6-6 6-6" />
    </svg>
  );
};

interface CarouselProps {
  children: ReactNode[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  className?: string;
  slidesToShow?: number;
  centerMode?: boolean;
  isScale?: boolean;
  loop?: boolean;
  dragFree?: boolean;
  continuousScrolling?: boolean;
  continuousScrollingSpeed?: number;
  continuousScrollingDirection?: "forward" | "backward";
}

export function Carousel({
  children,
  autoPlay = true,
  autoPlayInterval = 5000,
  showDots = false,
  showArrows = false,
  className,
  slidesToShow = 1,
  centerMode = false,
  isScale = false,
  loop = true,
  dragFree = false,
  continuousScrolling = false,
  continuousScrollingSpeed = 5,
  continuousScrollingDirection = "forward",
}: CarouselProps) {
  const [mounted, setMounted] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Configure auto-scroll plugin options with optimized behavior
  const autoScrollOptions = continuousScrolling
    ? [
        AutoScroll({
          speed: continuousScrollingSpeed,
          direction:
            continuousScrollingDirection === "backward"
              ? "backward"
              : "forward",
          stopOnInteraction: false, // Change to false to continue after interaction
          stopOnMouseEnter: false, // Keep this as false too if you want continuous scrolling during hover
          rootNode: (emblaRoot) => emblaRoot.parentElement as HTMLElement,
        }),
      ]
    : [];

  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop,
      skipSnaps: false, // Prevents skipping slides during fast scrolling
      containScroll: "trimSnaps", // Helps with smooth edge transitions
      dragFree: dragFree, // Enforces snap points for more controlled scrolling
    },
    autoScrollOptions
  );

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setIsTransitioning(false);
  }, [emblaApi]);

  useEffect(() => {
    setMounted(true);

    if (!emblaApi) return;

    emblaApi.on("select", onSelect);
    emblaApi.on("settle", () => {
      setIsTransitioning(false);
    });
    emblaApi.on("scroll", () => setIsTransitioning(true));

    onSelect();

    return () => {
      emblaApi.off("select", onSelect);
      emblaApi.off("settle", () => {
        setIsTransitioning(false);
      });
      emblaApi.off("scroll", () => setIsTransitioning(true));
    };
  }, [emblaApi, onSelect]);

  const startAutoPlay = useCallback(() => {
    if (!emblaApi || !autoPlay || continuousScrolling) return;

    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
    }

    autoPlayRef.current = setInterval(() => {
      if (!isTransitioning) {
        emblaApi.scrollNext();
      }
    }, autoPlayInterval);
  }, [
    emblaApi,
    autoPlay,
    autoPlayInterval,
    isTransitioning,
    continuousScrolling,
  ]);

  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = null;
    }
  }, []);

  useEffect(() => {
    // Only start interval-based autoplay if continuousScrolling is not enabled
    if (!continuousScrolling) {
      startAutoPlay();
    }

    // Event handling for both continuous scrolling and regular autoplay
    const onPointerDown = () => {
      if (!continuousScrolling) {
        stopAutoPlay();
      }
      // Note: For continuous scrolling, the plugin handles pausing automatically on interaction
    };

    const onPointerUp = () => {
      if (!continuousScrolling) {
        startAutoPlay();
      }
      // Note: For continuous scrolling, the plugin handles resuming automatically after interaction
    };

    if (emblaApi) {
      emblaApi.on("pointerDown", onPointerDown);
      document.addEventListener("pointerup", onPointerUp);
    }

    return () => {
      stopAutoPlay();
      if (emblaApi) {
        emblaApi.off("pointerDown", onPointerDown);
        document.removeEventListener("pointerup", onPointerUp);
      }
    };
  }, [emblaApi, startAutoPlay, stopAutoPlay, continuousScrolling]);

  const onPointerUp = () => {
    if (continuousScrolling && emblaApi && emblaApi.plugins().autoScroll) {
      // Small timeout to ensure smooth transition back to auto-scrolling
      setTimeout(() => {
        emblaApi.plugins().autoScroll.play();
      }, 100);
    }
  };

  emblaApi?.on("pointerUp", onPointerUp);

  const scrollPrev = useCallback(() => {
    if (!emblaApi) return;
    if (!continuousScrolling) {
      stopAutoPlay();
      emblaApi.scrollPrev();
      startAutoPlay();
    } else {
      // For continuous scrolling, just scroll without affecting auto-scroll behavior
      emblaApi.scrollPrev();
      // Auto-scroll plugin will automatically resume after user interaction
    }
  }, [emblaApi, stopAutoPlay, startAutoPlay, continuousScrolling]);

  const scrollNext = useCallback(() => {
    if (!emblaApi) return;
    if (!continuousScrolling) {
      stopAutoPlay();
      emblaApi.scrollNext();
      startAutoPlay();
    } else {
      // For continuous scrolling, just scroll without affecting auto-scroll behavior
      emblaApi.scrollNext();
      // Auto-scroll plugin will automatically resume after user interaction
    }
  }, [emblaApi, stopAutoPlay, startAutoPlay, continuousScrolling]);

  const scrollTo = useCallback(
    (index: number) => {
      if (!emblaApi) return;
      if (!continuousScrolling) {
        stopAutoPlay();
        emblaApi.scrollTo(index);
        startAutoPlay();
      } else {
        // For continuous scrolling, just scroll without affecting auto-scroll behavior
        emblaApi.scrollTo(index);
        // Auto-scroll plugin will automatically resume after user interaction
      }
    },
    [emblaApi, stopAutoPlay, startAutoPlay, continuousScrolling]
  );

  const reflow = useCallback(() => {
    if (!emblaApi) return;
    emblaApi.reInit();
  }, [emblaApi]);

  useEffect(() => {
    window.addEventListener("resize", reflow);
    return () => window.removeEventListener("resize", reflow);
  }, [reflow]);

  if (!children || children.length === 0) {
    return null;
  }

  if (!mounted) {
    return (
      <div className={cn("relative py-12", className)}>
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="h-full w-full animate-pulse bg-gray-100"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative", className)}>
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex">
          {children.map((child, index) => {
            const isCurrent = index === selectedIndex;

            return (
              <div
                key={index}
                style={{
                  flex:
                    typeof window !== "undefined" && window.innerWidth < 768
                      ? "0 0 100%"
                      : `0 0 ${100 / slidesToShow}%`,
                }}
                className={cn(
                  "min-w-0",
                  isTransitioning ? "transition-opacity duration-300" : ""
                )}
              >
                <div
                  className={cn(
                    "flex transform flex-col items-center justify-center",
                    isScale
                      ? centerMode && isCurrent
                        ? "scale-100 transition-transform duration-300"
                        : "scale-[85%] transition-transform duration-300"
                      : "scale-100"
                  )}
                >
                  {child}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {showDots && children.length > 1 && (
        <div className="flex justify-center gap-2 pb-16 md:pb-7">
          {Array.from({ length: children.length }).map((_, index) => (
            <button
              key={index}
              onClick={() => scrollTo(index)}
              className={cn(
                "h-1.5 w-1.5 rounded-full transition-all duration-300",
                index === selectedIndex ? "w-5 bg-black" : "bg-gray-300"
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {showArrows && children.length > 1 && (
        <>
          <button
            className="absolute left-0 top-1/2 z-30 hidden h-12 w-12 -translate-y-1/2 md:flex"
            onClick={scrollPrev}
            aria-label="Previous slide"
          >
            <div className="flex h-full w-full items-center justify-center">
              <Previous />
            </div>
          </button>
          <button
            className="absolute right-0 top-1/2 z-30 hidden h-12 w-12 -translate-y-1/2 md:flex"
            onClick={scrollNext}
            aria-label="Next slide"
          >
            <div className="flex h-full w-full items-center justify-center">
              <Next />
            </div>
          </button>
        </>
      )}
    </div>
  );
}
