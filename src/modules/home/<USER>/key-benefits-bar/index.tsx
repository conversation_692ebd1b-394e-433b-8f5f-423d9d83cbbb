import React, { <PERSON> } from "react";

import Image from "next/image";

import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";

import { KeyBenefitsBarProps } from "./utils/types";
import Text from "@modules/ui-elements/text";

const KeyBenefitsBar: FC<DynamicPageComponentProps<KeyBenefitsBarProps>> = ({
  block,
}) => {
  const { benefits_bar } = block;

  return (
    <div className="bg-theme-background-cards">
      <div className="flex flex-col flex-wrap items-center gap-7 py-6 uppercase md-container md:flex-row md:justify-between md:gap-10">
        {benefits_bar.map((item) => (
          <div
            key={item.title}
            className="flex flex-col items-center gap-2 md:flex-row"
          >
            <Image
              src={item.logo.url}
              width={item.logo.width}
              height={item.logo.height}
              alt={item.title}
            />
            <Text as="p" size="body_large" className="font-[500] body-copy">
              {item.title}
            </Text>
          </div>
        ))}
      </div>
    </div>
  );
};

export default KeyBenefitsBar;
