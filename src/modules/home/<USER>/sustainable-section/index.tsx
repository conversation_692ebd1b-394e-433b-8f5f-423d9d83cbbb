import React, { <PERSON> } from "react";

import Link from "next/link";

import Banner from "@modules/common/banner";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { SustainableSectionProps } from "./utils/types";

const SustainableSection: FC<
  DynamicPageComponentProps<SustainableSectionProps>
> = ({ block }) => {
  const { sustainable_section } = block;
  const sustainable_media = sustainable_section.media[0];

  return (
    <div className="mt-[79px] md:mt-[83px]">
      <Banner
        src={sustainable_media.desktop_media.url}
        alt={sustainable_media.title}
        className="h-screen"
        autoPlay
        content={
          <div className="absolute bottom-[4.5%] w-full text-theme-text-contrast">
            <div className="flex flex-col items-center justify-center text-center content-container">
              <Text as="h1" size="display" className="text-theme-text-contrast">
                {sustainable_media.title}
              </Text>
              <Text
                as="p"
                className="text-theme-text-contrast lg:max-w-[42.5%]"
              >
                {sustainable_media.description}
              </Text>
              <Link
                href={sustainable_media.cta_link || "/"}
                passHref
                className="mt-2 block sm:mt-6"
              >
                <Button theme="contrast" className="h-[38px]">
                  {sustainable_media.cta_title}
                </Button>
              </Link>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default SustainableSection;
