import React, { <PERSON> } from "react";

import Link from "next/link";

import Banner from "@modules/common/banner";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Button from "@modules/ui-elements/button";

import { CategoryShowcaseProps } from "./utils/types";
import Text from "@modules/ui-elements/text";

const CategoryShowcase: FC<
  DynamicPageComponentProps<CategoryShowcaseProps>
> = ({ block }) => {
  const { category_showcase } = block;

  return (
    <div className="bg-theme-background-primary content-container">
      <div className="py-16 text-center md:py-20">
        <Text as="h1" size="display" className="text-theme-text-primary">
          SHOP BY CATEGORY
        </Text>
      </div>
      <div className="grid grid-cols-1 gap-5 md:grid-cols-2 md:gap-0">
        {category_showcase.media.map((category, index) => (
          <Banner
            key={index}
            src={category.desktop_media.url}
            alt={category.title}
            className="h-[700px] object-cover md:object-cover"
            content={
              <div className="absolute bottom-[7.5%] flex w-full flex-col items-center text-theme-text-contrast">
                <Text
                  as="h2"
                  size="headline"
                  className={`text-center ${
                    index === 1 || index === 2
                      ? "text-theme-text-primary"
                      : "text-theme-text-contrast"
                  }`}
                >
                  {category.title}
                </Text>
                <Link
                  href={category.cta_link || "/"}
                  passHref
                  className="block sm:mt-6"
                >
                  <Button theme="contrast" className="h-[38px]">
                    {category.cta_title}
                  </Button>
                </Link>
              </div>
            }
          />
        ))}
      </div>
    </div>
  );
};

export default CategoryShowcase;
