import React, { <PERSON> } from "react";

import Image from "next/image";

import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";

import { KeyBenefitsBarProps } from "./utils/types";

const KeyBenefitsBarSmall: FC<
  DynamicPageComponentProps<KeyBenefitsBarProps>
> = ({ block }) => {
  const { benefits_bar } = block;

  return (
    <div className="grid w-full grid-cols-3 items-center justify-between uppercase">
      {benefits_bar.map((item) => (
        <div
          key={item.title}
          className="flex flex-col items-center justify-center gap-2"
        >
          <Image
            src={item.logo.url}
            width={item.logo.width}
            height={item.logo.height}
            alt={item.title}
          />
          <p className="text-center body-copy-2">{item.title}</p>
        </div>
      ))}
    </div>
  );
};

export default KeyBenefitsBarSmall;
