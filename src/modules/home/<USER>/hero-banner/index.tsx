import React, { <PERSON> } from "react";

import Link from "next/link";

import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";
import Banner from "modules/common/banner";

import { HeroSectionProps } from "./utils/types";

const HeroBanner: FC<DynamicPageComponentProps<HeroSectionProps>> = ({
  block,
}) => {
  const { hero_banner } = block;

  const hero_media = hero_banner.media[0];

  return (
    <Banner
      src={hero_media.desktop_media.url}
      alt="Mayave Jewellery Collection"
      className="h-screen"
      autoPlay={true}
      poster={hero_media.thumbnail.url}
      content={
        <div className="absolute bottom-[10%] left-[10%] text-theme-text-contrast sm:bottom-[90px] sm:left-[66px] lg:bottom-[70px] lg:left-[110px]">
          <Text as="h1" size="display" className="text-theme-text-contrast">
            {hero_media.title}
          </Text>
          <Text
            as="p"
            size="body_medium"
            className="block text-theme-text-contrast"
          >
            {hero_media.subtitle}
          </Text>
          <Link
            href={hero_media.cta_link || "/"}
            passHref
            className="mt-2 block sm:mt-6"
          >
            <Button className="h-[38px] w-24" theme="contrast">
              {hero_media.cta_title}
            </Button>
          </Link>
        </div>
      }
    />
  );
};

export default HeroBanner;
