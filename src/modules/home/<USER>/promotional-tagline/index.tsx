import { FC } from "react";

import Link from "next/link";

import RichtextBlockRenderer from "@modules/common/richtext-block-renderer";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { PromotionalTaglineProps } from "./utils/types";

const PromotionalTagline: FC<
  DynamicPageComponentProps<PromotionalTaglineProps>
> = ({ block }) => {
  const { promotional_tagline } = block;

  const { title, cta_link, cta_title, richtext_description, description } =
    promotional_tagline;

  return (
    <div className="bg-theme-background-accent py-[60px]">
      <div className="text-center md-container md:text-left">
        <Text as="h1" size="display" className="text-theme-text-contrast">
          {title}
        </Text>
        <Text
          as="span"
          size="body_large"
          className="block max-w-[569px] text-theme-text-contrast"
        >
          {richtext_description && (
            <RichtextBlockRenderer content={richtext_description} />
          )}

          {description && <>{description}</>}
        </Text>
        <Link
          href={cta_link}
          passHref
          className="mt-6 flex w-full justify-center sm:mt-6 lg:justify-start"
        >
          <Button className="h-[38px] w-fit" theme="contrast">
            {cta_title}
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default PromotionalTagline;
