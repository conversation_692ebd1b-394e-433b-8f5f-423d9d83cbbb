import { Media } from "utils/types/common";

interface CollectionImage {
  src: string;
  alt: string;
  description: string;
  size?: {
    width: number;
    height: number;
  };
}

export interface CollectionData {
  title: string;
  tagline: string;
  description: string;
  images: CollectionImage[];
}

export interface HeroMediaCenterBannerType {
  id: string;
  __typename: "ComponentCollectionPlpHeroCenterBanner";
  media: Media;
  cta_title: string;
  cta_link: string;
}

export interface CollectionShowcaseImage {
  id: string;
  description: string | null;
  media: Media;
}

export interface CollectionShowcaseSection {
  __typename: "ComponentCollectionPlpCollectionShowcaseSection";
  id: string;
  title: string;
  tagline: string;
  description: string;
  images: CollectionShowcaseImage[];
}

export interface CollectionShowcaseType {
  __typename: "ComponentCollectionPlpCollectionShowcase";
  id: string;
  collection_showcase_section: CollectionShowcaseSection[];
}

export interface PressAndHoldSection {
  __typename: "ComponentCollectionPlpPressAndHold";
  id: string;
  background_video: Media;
  title: string;
  default_icon: Media;
  pressed_icon: Media;
}

export interface PressHoldType {
  __typename: "ComponentCollectionPlpPressHold";
  id: string;
  press_and_hold_section: PressAndHoldSection;
}

export interface CollectionDetailsSection {
  __typename: "ComponentCollectionPlpCollectionDetailsSection";
  id: string;
  description: string;
  column_image: Media;
  cell_media: Media;
}

export interface CollectionDetailsType {
  __typename: "ComponentCollectionPlpCollectionDetails";
  id: string;
  collection_details_section: CollectionDetailsSection;
}
