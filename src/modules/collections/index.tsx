import React from "react";

import { notFound } from "next/navigation";

import { getPageTemplateName } from "utils/api/strapi-api";

import DynamicCollections from "./components";

async function Collections({ collection }: { collection: string }) {
  const page = `collection/${collection}`;

  const [{ template_name, page_type }] = await Promise.all([
    getPageTemplateName(page),
  ]);

  if (!template_name || !page_type) {
    console.warn(`-> No template name found for page: ${page}`);
    notFound();
  }

  return (
    <DynamicCollections
      page={page}
      page_type={page_type}
      template_name={template_name}
    />
  );
}

export default Collections;
