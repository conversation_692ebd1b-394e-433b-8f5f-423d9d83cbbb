import { CollectionShowcaseType } from "@modules/collections/utils/types";
import MediaDisplay from "@modules/common/media-display";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";

export default function CollectionShowcase({
  block,
}: DynamicPageComponentProps<CollectionShowcaseType>) {
  const collectionShowCase = block?.collection_showcase_section?.[0] || {};

  return (
    <>
      <div className="mx-auto px-5 py-10 md:px-8 md:py-12">
        <div className="flex flex-col gap-3 text-center md:text-left lg:gap-5">
          <h2 className="text-theme-text-primary h1">
            {collectionShowCase?.title}
          </h2>
          <h3 className="text-theme-text-primary h2">
            {collectionShowCase?.tagline}
          </h3>
          <div className="w-full md:w-[460px]">
            <p className="text-theme-text-secondary body-copy">
              {collectionShowCase?.description}
            </p>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-5 md:mt-9 lg:gap-10">
        <div className="flex flex-col items-center gap-10 md:flex-row md:gap-[134px]">
          {collectionShowCase?.images?.map((image, index) => (
            <div
              key={index}
              className={`flex w-[300px] flex-row justify-center md:flex-col md:px-0 ${
                index === 1 ? `md:w-[472px]` : "md:w-[710px]"
              } items-center`}
            >
              <div className="">
                <MediaDisplay
                  src={image?.media?.url}
                  alt={image?.media?.alternativeText}
                  className={`${
                    index === 1
                      ? `h-[350px] md:h-[551px]`
                      : "h-[361px] md:h-[855px]"
                  }`}
                />
              </div>
            </div>
          ))}
        </div>

        <div className="p-5 pb-10 md:ml-[72px] md:px-0 md:pb-[84px] md:pt-10">
          {collectionShowCase?.images?.map((image, index) => (
            <div key={index} className="flex justify-center md:justify-start">
              {image?.description && (
                <div
                  className={`flex w-[300px] flex-col items-center justify-center md:w-[460px]`}
                >
                  <p className="text-center text-sm leading-relaxed text-gray-700 body-copy md:w-full md:text-left">
                    {image?.description}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
}
