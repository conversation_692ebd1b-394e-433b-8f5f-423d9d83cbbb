import { notFound } from "next/navigation";

import { DynamicPageProps } from "@modules/dynamic-page/utils/types";
import ProductListing from "@modules/store/components/product-listing";
import { getTemplateBlocks } from "utils/api/strapi-api";
import renderBlock from "utils/mappers/component-renderer";
import { getCollectionPLPTemplateBlocksQuery } from "utils/strapi-api/collection-plp-template-query";

async function DynamicCollections({
  page,
  page_type,
  template_name,
}: DynamicPageProps) {
  const blocks = await getTemplateBlocks(
    template_name,
    getCollectionPLPTemplateBlocksQuery
  );

  if (blocks.length === 0) notFound();

  const rendered_blocks = blocks.map((block, index) =>
    renderBlock(block, index, page, page_type)
  );

  const promotionalBlock = blocks.find(
    (item) => item.__typename === "ComponentStorePromotionalBanner"
  );

  rendered_blocks.push(
    <ProductListing key="static-block" block={promotionalBlock} />
  );

  return <>{rendered_blocks}</>;
}

export default DynamicCollections;
