import React from "react";

import { HeroMediaCenterBannerType } from "@modules/collections/utils/types";
import Banner from "@modules/common/banner";
import NextLink from "@modules/common/next-link";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";
import Button from "@modules/ui-elements/button";

function HeroMediaCenterBanner({
  block,
}: DynamicPageComponentProps<HeroMediaCenterBannerType>) {
  const hero_media = block?.media || {};

  return (
    <Banner
      src={hero_media?.url}
      alt={hero_media?.alternativeText}
      className="h-screen"
      autoPlay={true}
      content={
        <div className="absolute bottom-10 left-1/2 translate-x-[-50%] text-theme-text-contrast">
          <NextLink
            href={block?.cta_link || "/"}
            passHref
            className="mt-2 block sm:mt-6"
          >
            <Button
              className="text-theme-text-contrast' bg-theme-background-secondary"
              theme="default"
            >
              {block?.cta_title}
            </Button>
          </NextLink>
        </div>
      }
    />
  );
}

export default HeroMediaCenterBanner;
