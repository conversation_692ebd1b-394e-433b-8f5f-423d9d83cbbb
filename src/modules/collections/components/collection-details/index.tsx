import React from "react";

import { CollectionDetailsType } from "@modules/collections/utils/types";
import MediaDisplay from "@modules/common/media-display";
import { DynamicPageComponentProps } from "@modules/dynamic-page/utils/types";

const CollectionDetails = ({
  block,
}: DynamicPageComponentProps<CollectionDetailsType>) => {
  const collectionDetails = block?.collection_details_section || {};

  return (
    <div className="grid w-full grid-cols-1 md:grid-cols-2 md:grid-rows-2">
      <div className="order-1 flex items-center justify-center md:order-3 md:col-start-1 md:row-start-2">
        <MediaDisplay
          src={collectionDetails?.cell_media?.url}
          alt={collectionDetails?.cell_media?.alternativeText}
          className="h-[424px]"
        />
      </div>

      <div className="order-2 flex max-w-[570px] items-center px-6 py-12 md:order-1 md:col-start-1 md:row-start-1 md:px-12 md:py-[147px]">
        <p className="text-left body-copy">{collectionDetails?.description}</p>
      </div>

      <div className="order-3 md:order-2 md:col-start-2 md:row-span-2 md:row-start-1">
        <MediaDisplay
          src={collectionDetails?.column_image?.url}
          alt={collectionDetails?.column_image?.alternativeText}
          className="h-full w-full object-cover"
        />
      </div>
    </div>
  );
};

export default CollectionDetails;
