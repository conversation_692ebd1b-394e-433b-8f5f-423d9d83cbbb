import { HttpTypes } from "@medusajs/types";
import NextLink from "@modules/common/next-link";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";
import { convertToLocale } from "utils/helpers/common";

const SummaryRow = ({
  label,
  value,
  description,
  isBold,
}: {
  label: string;
  value: string;
  description?: string;
  isBold?: boolean;
}) => (
  <div className="flex items-start justify-between">
    <div>
      <Text as="p" size="body_large">
        {label}
      </Text>
      {description && (
        <Text
          as="p"
          size="body_medium"
          className="block max-w-[80%] text-theme-text-secondary"
        >
          {description}
        </Text>
      )}
    </div>
    <Text
      as="p"
      size={isBold ? "body_semi_bold" : "body_large"}
      className="whitespace-nowrap"
    >
      {value}
    </Text>
  </div>
);

const CartSummary = ({ cart }: { cart: HttpTypes.StoreCart }) => {
  const {
    currency_code,
    total,
    subtotal,
    tax_total,
    shipping_subtotal,
    discount_total,
  } = cart;
  return (
    <div className="h-fit w-full space-y-[30px] py-5 lg:space-y-[26px] lg:border lg:border-theme-other-black lg:p-[30px]">
      <Text as="h6" size="body_semi_bold">
        ORDER CONFIRMATION
      </Text>
      <div className="flex flex-col">
        <SummaryRow
          label="Subtotal"
          value={convertToLocale({ amount: subtotal ?? 0, currency_code })}
        />
        <SummaryRow
          label="Shipping"
          value={convertToLocale({
            amount: shipping_subtotal ?? 0,
            currency_code,
          })}
        />
        <SummaryRow
          label="Tax"
          value={convertToLocale({ amount: tax_total ?? 0, currency_code })}
          description="(Will be calculated according to your delivery address)"
        />
        {discount_total ? (
          <SummaryRow
            label="Discount"
            value={convertToLocale({
              amount: discount_total ?? 0,
              currency_code,
            })}
          />
        ) : null}
      </div>
      <div className="mt-[45px] flex flex-col gap-2.5 lg:mt-10">
        <SummaryRow
          label="Total"
          value={convertToLocale({ amount: total ?? 0, currency_code })}
          isBold
        />
        <NextLink href="/checkout">
          <Button className="w-full" theme="primary">
            Proceed to Checkout
          </Button>
        </NextLink>
      </div>
    </div>
  );
};

export default CartSummary;
