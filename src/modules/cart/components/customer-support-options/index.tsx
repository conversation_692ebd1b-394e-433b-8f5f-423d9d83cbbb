import React from "react";

import NextLink from "@modules/common/next-link";
import Text from "@modules/ui-elements/text";
import ArrowRight from "assets/icons/arrow-right";

import { CUSTOMER_SUPPORT_OPTIONS } from "./utils/data";

const CustomerSupportOptions = () => {
  return (
    <div className="flex flex-col gap-2.5">
      {CUSTOMER_SUPPORT_OPTIONS.map((option) => (
        <NextLink
          key={option.id}
          href={option.link}
          className="relative flex min-h-[127px] items-center gap-7 bg-theme-background-cards pl-7 md:pl-10"
        >
          <div className="text-theme-background-accent">{option.icon}</div>
          <div className="flex flex-col gap-2">
            <Text as="p" size="body_large" className="leading-[100%]">
              {option.title}
            </Text>
            <Text as="p" size="body_medium">
              {option.description}
            </Text>
          </div>
          <div className="absolute right-[11px] top-1/2 -translate-y-1/2 md:right-[18px]">
            <ArrowRight className="w-1.5" />
          </div>
        </NextLink>
      ))}
    </div>
  );
};

export default CustomerSupportOptions;
