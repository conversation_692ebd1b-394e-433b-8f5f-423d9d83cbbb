"use client";

import React, { useCallback, useState } from "react";

import { HttpTypes } from "@medusajs/types";
import Divider from "@modules/common/divider";
import MediaDisplay from "@modules/common/media-display";
import Button from "@modules/ui-elements/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@modules/ui-elements/select";
import Text from "@modules/ui-elements/text";
import { Loader2 } from "lucide-react";
import { deleteLineItem, updateLineItem } from "utils/api/server-api/cart";
import { convertToLocale } from "utils/helpers/common";

const CartItem = ({ item }: { item: HttpTypes.StoreCartLineItem }) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleDelete = async (id: string) => {
    setIsDeleting(true);
    await deleteLineItem(id)
      .then()
      .catch((err) => {
        console.error("Error deleting line item:", err);
        setIsDeleting(false);
      });
  };

  const handleQuantityChange = useCallback(
    async (newQuantity: string) => {
      const quantity = parseInt(newQuantity);
      if (quantity && quantity < 1) return;

      try {
        setIsUpdating(true);
        await updateLineItem({ lineId: item.id, quantity });
      } catch (error) {
        console.error("Failed to update quantity:", error);
      } finally {
        setIsUpdating(false);
      }
    },
    [item.id]
  );

  return (
    <div className="flex w-full flex-col gap-4 md:flex-row md:gap-[26px]">
      <div className="relative w-full md:max-w-[397px]">
        <MediaDisplay
          src={item.thumbnail || ""}
          className="h-[439px] md:max-w-[397px]"
        />
      </div>
      <div className="flex w-full flex-col justify-between md:w-[406px]">
        <div>
          <Text
            as="p"
            size="body_medium"
            className="block leading-[100%] text-theme-text-secondary"
          >
            {item.variant?.sku || "N/A"}
          </Text>
          <div className="mt-1 flex flex-col md:flex-row md:items-center md:justify-between">
            <Text as="p" size="body_semi_bold">
              {item.title}
            </Text>
            <Text as="p" size="body_semi_bold">
              {convertToLocale({ amount: item.total })}
            </Text>
          </div>
          <Divider className="my-3" />
          <div className="space-y-1">
            <div className="flex flex-row items-center justify-between">
              <Text as="p" size="body_medium">
                Color
              </Text>
              <Text as="p" size="body_medium">
                White Gold
              </Text>
            </div>
            <div className="flex flex-row items-center justify-between">
              <Text as="p" size="body_medium">
                Size
              </Text>
              <Text as="p" size="body_medium">
                NSA
              </Text>
            </div>
            <div className="w-[60]">
              <Select
                onValueChange={handleQuantityChange}
                value={item.quantity.toString()}
                disabled={isUpdating}
                required
              >
                <SelectTrigger className="h-10 w-full capitalize">
                  {isUpdating ? (
                    <Loader2 className="size-4 animate-spin" />
                  ) : (
                    <SelectValue placeholder={0} />
                  )}
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {[1, 2, 3, 4, 5, 6].map((q, index) => (
                      <SelectItem key={index} value={q.toString()}>
                        {q}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        {/* CTA ICONS */}
        <div className="mt-4 flex flex-row gap-2.5">
          <Button>Add to Wishlist</Button>
          <Button onClick={() => handleDelete(item.id)} loading={isDeleting}>
            Remove
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CartItem;
