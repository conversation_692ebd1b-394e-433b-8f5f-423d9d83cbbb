import React from "react";

import { HttpTypes } from "@medusajs/types";
import CartItem from "@modules/cart/components/cart-item";
import CartSummary from "@modules/cart/components/cart-summary";
import CustomerSupportOptions from "@modules/cart/components/customer-support-options";
import NextLink from "@modules/common/next-link";
import Text from "@modules/ui-elements/text";

const Cart = ({
  cart,
  customer,
}: {
  cart: HttpTypes.StoreCart | null;
  customer: HttpTypes.StoreCustomer | null;
}) => {
  console.log({ customer, cart });
  return (
    <div className="py-10 content-container lg:py-[84px]">
      <div className="mb-5 flex items-center justify-between gap-4 md:mb-[46px]">
        <Text as="h6" size="body_medium">
          My Shopping Cart ({cart?.items?.length || 0})
        </Text>
        <NextLink href="/jewellery" className="underline body-copy-2">
          Continue Shopping
        </NextLink>
      </div>
      {!cart?.items || cart?.items?.length === 0 ? (
        <div className="flex h-[400px] w-full flex-col items-center justify-center gap-10 border border-theme-other-black lg:flex-row lg:gap-[60px]">
          <div>No items in cart</div>
        </div>
      ) : (
        <div className="flex w-full flex-col gap-10 lg:flex-row lg:justify-between lg:gap-[60px]">
          {cart?.items?.map((item) => (
            <div key={item.id}>
              <CartItem item={item} />
            </div>
          ))}
          <div className="flex w-full flex-col gap-10 lg:max-w-[443px] lg:gap-5">
            <CartSummary cart={cart} />
            <CustomerSupportOptions />
          </div>
        </div>
      )}
    </div>
  );
};

export default Cart;
