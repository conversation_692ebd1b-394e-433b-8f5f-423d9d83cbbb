import React from "react";

import MediaDisplay from "@modules/common/media-display";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { GiftCategoryCard } from "../gifting-categories/utils/types";

function GiftCard({ gift_card }: GiftCategoryCard) {
  return (
    <div className="flex w-full flex-col items-center justify-start gap-5">
      <MediaDisplay
        src={gift_card.desktop_media.url}
        className="h-[500px] w-full lg:h-[700px] lg:w-[589px]"
      />
      <div className="flex w-full flex-col items-start justify-start gap-1 pl-0 lg:pl-9">
        <Text as="p" size="headline">
          {gift_card.title}
        </Text>
        <Text className="lg:w-[589px]" size="body_large">
          {gift_card.description}
        </Text>
        <Button className="mt-2 w-fit">{gift_card.cta_title}</Button>
      </div>
    </div>
  );
}

export default GiftCard;
