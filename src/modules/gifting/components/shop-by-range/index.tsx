import React from "react";

import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { ShopByPriceBlock } from "./utils/types";

function ShopByPriceRange({ block }: { block: ShopByPriceBlock }) {
  return (
    <div className="flex w-full flex-col items-center justify-start gap-5 bg-theme-background-cards p-5 lg:items-start lg:gap-10 lg:p-20">
      <Text
        size="display"
        className="w-[300px] text-center lg:w-full lg:text-left"
      >
        {block.title}
      </Text>
      <div className="flex w-full flex-col items-center justify-start gap-5 lg:flex-row lg:gap-10">
        {block?.price_range_cta?.map((cta, index) => {
          return <Button key={`cta-${index}`}>{cta.title}</Button>;
        })}
      </div>
    </div>
  );
}

export default ShopByPriceRange;
