"use client";

import Link from "next/link";

import Banner from "@modules/common/banner";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { GiftPromotionBlock } from "./utils/types";

function GiftPromotion({ block }: { block: GiftPromotionBlock }) {
  const { gift_card_promotion } = block;

  const giftCardMedia = gift_card_promotion.media[0];

  return (
    <div className="my-[79px] bg-theme-background-primary content-container md:my-[83px]">
      <div className="flex flex-col gap-8 sm:px-[60px] md:flex-row-reverse md:gap-0 md:px-0">
        <div className="hidden flex-1 lg:block">
          <Banner
            src={giftCardMedia.desktop_media.url}
            alt={giftCardMedia.title}
            className="h-[790px] md:h-[700px]"
          />
        </div>
        <div className="flex flex-1 flex-col items-center justify-center">
          <div className="flex max-w-[90%] flex-col gap-3 text-center md:max-w-[373px] md:text-left">
            <Text as="h2" size="display" className="text-theme-text-primary">
              {giftCardMedia.title}
            </Text>
            <Text
              as="p"
              size="body_semi_bold"
              className="text-theme-text-primary"
            >
              {giftCardMedia.subtitle}
            </Text>
            <div className="block flex-1 lg:hidden">
              <Banner
                src={giftCardMedia.desktop_media.url}
                alt={giftCardMedia.title}
                className="h-[379px] w-[300px]"
              />
            </div>
            <Text as="p" size="body_large" className="text-theme-text-primary">
              {giftCardMedia.description}
            </Text>
            <Link
              href={giftCardMedia.cta_link || "/"}
              passHref
              className="flex w-full justify-center sm:mt-6 md:justify-start"
            >
              <Button className="h-[38px] w-fit">
                {giftCardMedia.cta_title}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GiftPromotion;
