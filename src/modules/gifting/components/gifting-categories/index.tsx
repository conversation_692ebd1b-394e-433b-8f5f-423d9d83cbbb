import GiftCard from "../gift-card";
import { ComponentGiftingGiftingCategory } from "./utils/types";

function GiftingCategories({
  block,
}: {
  block: ComponentGiftingGiftingCategory;
}) {
  const { heading, subheading, gift_category_cards } = block;

  return (
    <div className="flex w-full flex-col items-center justify-center gap-10 p-5 lg:gap-20 lg:p-20">
      <div className="flex w-full flex-col items-center justify-center gap-5 pt-5 lg:pt-0">
        <p className="h1">{heading}</p>
        <p className="w-full text-center body-copy lg:w-2/5">{subheading}</p>
      </div>

      <div className="flex w-full flex-col justify-center gap-5 lg:flex-row lg:gap-0">
        {gift_category_cards.map((card, index: number) => {
          return (
            <div key={`card-${index}`}>
              <GiftCard {...card} />
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default GiftingCategories;
