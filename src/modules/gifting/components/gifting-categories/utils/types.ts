import { Media } from "utils/types/common";

export interface GiftCard {
  id: string;
  title: string;
  subtitle: string | null;
  cta_title: string;
  cta_link: string;
  desktop_media: Media;
  mobile_media: Media;
  description: string;
  responsive: string;
  theme: string | null;
}

export interface GiftCategoryCard {
  id: string;
  gift_card: GiftCard;
}

export interface ComponentGiftingGiftingCategory {
  id: string;
  __typename: string;
  heading: string;
  subheading: string;
  gift_category_cards: GiftCategoryCard[];
}
