"use client";

import MediaDisplay from "@modules/common/media-display";
import Button from "@modules/ui-elements/button";
import Text from "@modules/ui-elements/text";

import { SpecialGiftBlock } from "./utils/types";

function SpecialGifts({ block }: { block: SpecialGiftBlock }) {
  return (
    <div className="flex w-full flex-col items-center justify-start gap-5 p-5 lg:flex-row lg:items-start lg:gap-20 lg:p-20">
      <Text
        size="display"
        className="w-[300px] text-center lg:w-[200px] lg:text-left"
      >
        {block.heading}
      </Text>
      <div className="flex w-full flex-col items-start justify-start gap-5 lg:flex-row lg:overflow-x-auto lg:pl-10">
        {block?.special_gift_card?.map(({ gift_card }, index) => {
          return (
            <div key={`card-${index}`} className="w-full lg:min-w-[459px]">
              <div className="flex w-full flex-col items-start justify-start gap-5">
                <MediaDisplay
                  src={gift_card.desktop_media.url}
                  className="h-[500px] w-full lg:h-[568px] lg:w-[459px]"
                />
                <div className="flex w-full flex-col items-start justify-start gap-1">
                  <Text as="p" size="headline">
                    {gift_card.title}
                  </Text>
                  <Text className="w-[90%]" size="body_large">
                    {gift_card.description}
                  </Text>
                  <Button className="mt-2 w-fit">{gift_card.cta_title}</Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default SpecialGifts;
