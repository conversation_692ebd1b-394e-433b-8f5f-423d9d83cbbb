import { Suspense } from "react";

import { notFound } from "next/navigation";

import { DynamicPageProps } from "@modules/dynamic-page/utils/types";
import { getTemplateBlocks } from "utils/api/strapi-api";
import renderBlock from "utils/mappers/component-renderer";
import { getGiftingPageTemplateBlocksQuery } from "utils/strapi-api/gifting-template-query";

async function DynamicGifting(Props: DynamicPageProps) {
  const { page, page_type } = Props;

  const blocks = await getTemplateBlocks(
    Props.template_name,
    getGiftingPageTemplateBlocksQuery
  );

  if (blocks.length === 0) notFound();

  const rendered_blocks = blocks.map((block, index) =>
    renderBlock(block, index, page, page_type)
  );

  return <Suspense>{rendered_blocks}</Suspense>;
}

export default DynamicGifting;
