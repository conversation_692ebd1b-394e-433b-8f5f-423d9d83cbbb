import React from "react";

import cn from "utils/helpers/cn";

type TextSize =
  | "display"
  | "headline"
  | "body_large"
  | "body_medium"
  | "body_semi_bold"
  | "cta";

type TextElement =
  | "p"
  | "span"
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "label";

interface TextProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  className?: string;
  size?: TextSize;
  responsive?: boolean;
  as?: TextElement;
  htmlFor?: string;
}

const text_sizes = {
  display: "h1",
  headline: "h2",
  body_large: "body-copy",
  body_medium: "body-copy-2",
  body_semi_bold: "body-copy-semi-bold",
  cta: "cta-copy",
} as const;

export const Text: React.FC<TextProps> = ({
  children,
  className = "",
  size = "body_large",
  as: Component = "p",
  ...restProps
}) => {
  return (
    <Component
      className={cn(
        text_sizes[size],
        "inline-block text-theme-text-primary",
        className
      )}
      {...restProps}
    >
      {children}
    </Component>
  );
};

export default Text;
