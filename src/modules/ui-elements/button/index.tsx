"use client";

import React, { forwardRef } from "react";

import { twMerge } from "tailwind-merge";
import cn from "utils/helpers/cn";

type ButtonVariant = "primary";
type ButtonTheme = "contrast" | "default" | "primary";

interface ButtonStyleProps {
  variant?: ButtonVariant;
  theme?: ButtonTheme;
  loading?: boolean;
  loadingClassName?: string;
  disabled?: boolean;
  disabledClassName?: string;
}

interface ButtonProps
  extends ButtonStyleProps,
    React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  className?: string;
}

const VARIANT_STYLES: Record<ButtonVariant, string> = {
  primary: "h-10 w-full leading-tight px-4",
} as const;

const THEME_STYLES: Record<ButtonTheme, string> = {
  primary:
    "border border-theme-other-black text-theme-other-white bg-theme-other-black hover:bg-black/80 hover:text-theme-other-white",
  contrast:
    "border border-theme-other-white text-theme-other-white bg-transparent hover:bg-theme-other-white hover:text-theme-other-black",
  default:
    "border border-theme-other-black text-theme-other-black bg-transparent hover:bg-theme-other-black hover:text-theme-other-white",
} as const;

const LoadingSpinner = ({ className }: { className?: string }) => (
  <span
    className={twMerge("mr-2 inline-block animate-spin", className)}
    aria-hidden="true"
  >
    &#8635;
  </span>
);

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      className,
      variant = "primary",
      theme = "default",
      loading = false,
      loadingClassName,
      disabled = false,
      disabledClassName,
      onClick,
      ...props
    },
    ref
  ) => {
    const buttonClasses = cn(
      "flex items-center justify-center cta-copy transition-all duration-100 ease-in-out focus-visible:ring-[3px] focus-visible:ring-black/20 focus:outline-none",
      VARIANT_STYLES[variant],
      THEME_STYLES[theme],
      loading && "opacity-75 cursor-wait",
      disabled && "opacity-50 cursor-not-allowed",
      disabled && disabledClassName,
      className
    );

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) {
        e.preventDefault();
        return;
      }
      onClick?.(e);
    };

    return (
      <button
        ref={ref}
        className={buttonClasses}
        onClick={handleClick}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <LoadingSpinner className={loadingClassName} />
        ) : (
          <div>{children}</div>
        )}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
