"use client";

import React, { useCallback, useState } from "react";

import NextImage, {
  ImageProps as NextImageProps,
  StaticImageData,
} from "next/image";

import cn from "utils/helpers/cn";

import { Text } from "../text";

interface ImageProps extends NextImageProps {
  src: string | StaticImageData;
  alt: string;
  fallbackText?: string;
}

export const Image: React.FC<ImageProps> = React.memo(
  ({
    className = "",
    src,
    alt = "Image",
    width,
    height,
    fallbackText,
    ...restProps
  }) => {
    const [hasError, setHasError] = useState(false);

    const handleError = useCallback(() => {
      setHasError(true);
    }, []);

    if (!src || hasError) {
      return (
        <div
          className={cn("h-full w-full bg-white/20 object-cover", className)}
        >
          <Text
            size="cta"
            as="span"
            className="flex size-full items-center justify-center overflow-hidden rounded"
            sr-only={src}
          >
            {fallbackText || alt}
          </Text>
        </div>
      );
    }

    return (
      <NextImage
        className={cn("h-full w-full object-cover", className)}
        src={src}
        alt={alt || "Image"}
        width={width}
        height={height}
        onError={handleError}
        {...restProps}
      />
    );
  }
);

Image.displayName = "Image";

export default Image;

// sizes = "(max-width: 480px) 450px, (max-width: 576px) 560px, (max-width: 768px) 600px, (max-width: 992px) 800px, (max-width: 1200px) 1000px, 1320px";

// sizes = "(max-width: 768px) 336px, 600px";
