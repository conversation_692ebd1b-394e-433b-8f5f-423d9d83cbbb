"use client";

import React, {
  InputHTMLAttributes,
  ReactNode,
  useCallback,
  useRef,
} from "react";

import cn from "utils/helpers/cn";

import Text from "../text";

type InputVariant = "standard" | "outlined";

export interface InputProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, "prefix" | "suffix"> {
  // Style customization
  wrapperClassName?: string;
  inputClassName?: string;

  // Input elements
  label?: string;
  prefix?: ReactNode;
  suffix?: ReactNode;
  error?: string;
  variant?: InputVariant;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      // Layout customization
      className = "",
      wrapperClassName = "",
      inputClassName = "",

      // Input attributes
      name = "",
      placeholder = "",
      type = "text",
      label = "",
      prefix,
      suffix,
      error,
      disabled = false,
      required = false,
      value,
      variant = "standard",

      // Event handlers
      onChange,
      onBlur,
      onFocus,
      onClick,

      // Rest props
      ...restProps
    },
    ref
  ) => {
    // State management
    const inputRef = useRef<HTMLInputElement | null>(null);

    // Event handlers
    const handleFocus = useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        onFocus?.(e);
      },
      [onFocus]
    );

    const handleBlur = useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        onBlur?.(e);
      },
      [onBlur]
    );

    const handleLabelClick = useCallback(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        inputRef.current.click();
      }
    }, []);

    return (
      <div className="w-full">
        <div className={cn("flex flex-col gap-1", className)}>
          {variant === "outlined" && label && (
            <Text
              as="label"
              size="body_large"
              className={cn(
                "mb-1 leading-[100%] text-theme-text-primary",
                error && "text-theme-validations-error"
              )}
            >
              {label}
              {required && <span className="ml-px">*</span>}
            </Text>
          )}
          <div
            className={cn(
              "relative flex w-full items-center gap-1.5 overflow-hidden",
              variant === "standard" ? "border-b" : "border-[0.5px]",
              "medium-3 peer h-10 w-full rounded-none border-theme-other-border leading-[1.255] text-theme-text-primary transition-all",
              "disabled:bg-neutral-50 disabled:text-neutral-400",
              error &&
                "border-theme-validations-error focus:border-theme-validations-error",
              wrapperClassName
            )}
          >
            {prefix}
            <input
              ref={(node) => {
                inputRef.current = node;
                if (typeof ref === "function") {
                  ref(node);
                } else if (ref) {
                  ref.current = node;
                }
              }}
              className={cn(
                "medium-3 peer h-full w-full leading-[1.255] text-theme-text-primary outline-none focus-visible:ring-[3px] focus-visible:ring-black/20",
                variant === "outlined" && "px-2.5",
                "placeholder:text-[16px] placeholder:font-[300] placeholder:leading-normal placeholder:text-theme-text-secondary",
                "focus:outline-none",
                inputClassName
              )}
              type={type}
              name={name}
              placeholder={placeholder}
              onClick={onClick}
              onChange={onChange}
              autoComplete="off"
              onBlur={handleBlur}
              onFocus={handleFocus}
              disabled={disabled}
              value={value}
              {...restProps}
            />

            {suffix && (
              <span
                className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
                onClick={handleLabelClick}
              >
                {suffix}
              </span>
            )}
          </div>
        </div>
        {error && (
          <span className="mt-1 text-xs text-theme-validations-error">
            {error}
          </span>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
