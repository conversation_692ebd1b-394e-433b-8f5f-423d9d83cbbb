{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": "./src", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/store/*": ["store/*"], "@lib/*": ["lib/*"], "@modules/*": ["modules/*"], "@pages/*": ["pages/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/lib/data/default/home/<USER>", "check-env-variables.js", "tailwind.config.js"], "exclude": ["node_modules", ".next", ".nyc_output", "coverage", "jest-coverage"]}